2025-08-15 16:17:05,486 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 🚀 开始获取微信内容: https://mp.weixin.qq.com/s?__biz=Mzk0OTYwMjk4Nw==&mid=2247485670&idx=1&sn=2d9b0a41233acb7b97dcd6d16f2408ec&chksm=c3549722f4231e342148686a0b4f4847e0706051cfc384731c9d7909f031c1e9a660b4104c5a#rd
2025-08-15 16:17:05,486 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 📡 使用requests方法获取内容
2025-08-15 16:17:10,036 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:74] - WARNING: 页面包含验证内容，需要Selenium处理
2025-08-15 16:17:10,037 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 🔄 requests方法失败，尝试Selenium方法
2025-08-15 16:17:10,037 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 🤖 使用Selenium方法获取内容
2025-08-15 16:17:17,533 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 🔍 检测到验证页面，尝试处理...
2025-08-15 16:17:17,827 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: ✅ Selenium方法获取成功
2025-08-15 16:17:17,827 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 开始获取微信公众号数据: https://mp.weixin.qq.com/s?__biz=Mzk0OTYwMjk4Nw==&mid=2247485670&idx=1&sn=2d9b0a41233acb7b97dcd6d16f2408ec&chksm=c3549722f4231e342148686a0b4f4847e0706051cfc384731c9d7909f031c1e9a660b4104c5a#rd
2025-08-15 16:17:17,827 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 🚀 开始获取微信内容: https://mp.weixin.qq.com/s?__biz=Mzk0OTYwMjk4Nw==&mid=2247485670&idx=1&sn=2d9b0a41233acb7b97dcd6d16f2408ec&chksm=c3549722f4231e342148686a0b4f4847e0706051cfc384731c9d7909f031c1e9a660b4104c5a#rd
2025-08-15 16:17:17,827 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 📡 使用requests方法获取内容
2025-08-15 16:17:22,103 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:74] - WARNING: 页面包含验证内容，需要Selenium处理
2025-08-15 16:17:22,104 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 🔄 requests方法失败，尝试Selenium方法
2025-08-15 16:17:22,104 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 🤖 使用Selenium方法获取内容
2025-08-15 16:17:29,707 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 🔍 检测到验证页面，尝试处理...
2025-08-15 16:17:29,987 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: ✅ Selenium方法获取成功
2025-08-15 16:17:29,988 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 数据获取成功: 标题=【热线攻坚】区房管局联合街道开展“12345”热线住宅物业诉求分析研究专题工作会..., 内容长度=739
2025-08-15 16:17:39,991 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:55] - ERROR: mq_push_error:
2025-08-15 16:17:39,991 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 数据推送到消息队列成功
2025-08-15 16:17:39,992 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 🚀 开始获取微信内容: https://mp.weixin.qq.com/s?__biz=MzI0MTgwOTg4Mg==&mid=2247483975&idx=1&sn=95074fa9035aa0e285ed917834646d5f&scene=0
2025-08-15 16:17:39,992 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 📡 使用requests方法获取内容
2025-08-15 16:17:40,696 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:74] - WARNING: 被重定向到验证页面: https://mp.weixin.qq.com/mp/wappoc_appmsgcaptcha?poc_token=HCTtnmijRcquiGcLag_iyGZOfUffiIor29yWeeUG&target_url=https%3A%2F%2Fmp.weixin.qq.com%2Fs%3F__biz%3DMzI0MTgwOTg4Mg%3D%3D%26mid%3D2247483975%26idx%3D1%26sn%3D95074fa9035aa0e285ed917834646d5f%26scene%3D0
2025-08-15 16:17:40,698 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 🔄 requests方法失败，尝试Selenium方法
2025-08-15 16:17:40,698 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 🤖 使用Selenium方法获取内容
2025-08-15 16:17:45,617 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 🔍 检测到验证页面，尝试处理...
2025-08-15 16:17:45,827 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: ✅ Selenium方法获取成功
2025-08-15 16:17:45,827 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 开始获取微信公众号数据: https://mp.weixin.qq.com/s?__biz=MzI0MTgwOTg4Mg==&mid=2247483975&idx=1&sn=95074fa9035aa0e285ed917834646d5f&scene=0
2025-08-15 16:17:45,827 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 🚀 开始获取微信内容: https://mp.weixin.qq.com/s?__biz=MzI0MTgwOTg4Mg==&mid=2247483975&idx=1&sn=95074fa9035aa0e285ed917834646d5f&scene=0
2025-08-15 16:17:45,827 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 📡 使用requests方法获取内容
2025-08-15 16:17:46,249 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:74] - WARNING: 被重定向到验证页面: https://mp.weixin.qq.com/mp/wappoc_appmsgcaptcha?poc_token=HCrtnmijRst4i8ntvyVsXpDJZeI-LP-ppwSOMKNB&target_url=https%3A%2F%2Fmp.weixin.qq.com%2Fs%3F__biz%3DMzI0MTgwOTg4Mg%3D%3D%26mid%3D2247483975%26idx%3D1%26sn%3D95074fa9035aa0e285ed917834646d5f%26scene%3D0
2025-08-15 16:17:46,250 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 🔄 requests方法失败，尝试Selenium方法
2025-08-15 16:17:46,250 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 🤖 使用Selenium方法获取内容
2025-08-15 16:17:52,423 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 🔍 检测到验证页面，尝试处理...
2025-08-15 16:17:54,568 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: ✅ Selenium方法获取成功
2025-08-15 16:17:54,568 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 数据获取成功: 标题=..., 内容长度=0
2025-08-15 16:18:04,571 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:55] - ERROR: mq_push_error:
2025-08-15 16:18:04,572 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 数据推送到消息队列成功
2025-08-15 16:27:21,517 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 代理未启用或代理列表为空
2025-08-15 16:27:21,517 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 尝试第1次获取: https://mp.weixin.qq.com/s?__biz=Mzk0OTYwMjk4Nw==&mid=2247485670&idx=1&sn=2d9b0a41233acb7b97dcd6d16f2408ec&chksm=c3549722f4231e342148686a0b4f4847e0706051cfc384731c9d7909f031c1e9a660b4104c5a#rd
2025-08-15 16:27:21,517 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 尝试使用requests直接访问微信公众号
2025-08-15 16:27:23,815 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 不使用代理
2025-08-15 16:27:28,623 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:74] - WARNING: 页面包含验证内容，需要Selenium处理
2025-08-15 16:27:28,624 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: requests访问失败，尝试使用Selenium
2025-08-15 16:27:28,626 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 使用Selenium备用方案
2025-08-15 16:27:29,385 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 使用Chrome浏览器（本地ChromeDriver）
2025-08-15 16:27:34,460 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=Mzk0OTYwMjk4Nw==&mid=2247485670&idx=1&sn=2d9b0a41233acb7b97dcd6d16f2408ec&chksm=c3549722f4231e342148686a0b4f4847e0706051cfc384731c9d7909f031c1e9a660b4104c5a#rd
2025-08-15 16:27:34,463 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 页面标题: 【热线攻坚】区房管局联合街道开展“12345”热线住宅物业诉求分析研究专题工作会
2025-08-15 16:27:34,480 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 检测到验证页面，标识: verify
2025-08-15 16:27:34,486 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 检测到weui-msg验证页面元素
2025-08-15 16:27:34,494 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: weui-msg元素文本: 
2025-08-15 16:27:34,494 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 开始处理验证页面...
2025-08-15 16:27:36,551 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 第1次验证尝试...
2025-08-15 16:27:36,569 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 检测到文章内容元素: //div[@id="js_content"]
2025-08-15 16:27:36,569 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 已成功跳转到文章页面
2025-08-15 16:27:36,569 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 验证页面处理完成，重新获取页面内容
2025-08-15 16:27:40,238 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 未找到迁移页面元素: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//div[@class="weui-msg__opr-area"]/p/a[1]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x0000000100b41660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x0000000100b395d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x000000010068a0fc cxxbridge1$string$len + 90068
3   chromedriver                        0x00000001006d14a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x0000000100712874 cxxbridge1$string$len + 649036
5   chromedriver                        0x00000001006c5790 cxxbridge1$string$len + 333416
6   chromedriver                        0x0000000100b05440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x0000000100b086a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x0000000100ae6328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x0000000100b08f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x0000000100ad7414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x0000000100b29090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x0000000100b2921c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x0000000100b39214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-15 16:27:40,605 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: Selenium访问成功
2025-08-15 16:27:40,606 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 代理未启用或代理列表为空
2025-08-15 16:27:40,606 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 尝试第1次获取: https://mp.weixin.qq.com/s?__biz=MzI0MTgwOTg4Mg==&mid=2247483975&idx=1&sn=95074fa9035aa0e285ed917834646d5f&scene=0
2025-08-15 16:27:40,607 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 尝试使用requests直接访问微信公众号
2025-08-15 16:27:42,557 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 不使用代理
2025-08-15 16:27:43,188 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:74] - WARNING: 被重定向到验证页面: https://mp.weixin.qq.com/mp/wappoc_appmsgcaptcha?poc_token=HH7vnmijo0BkcTSEzzxWI1hUsRG1TPRiyWG0yh5d&target_url=https%3A%2F%2Fmp.weixin.qq.com%2Fs%3F__biz%3DMzI0MTgwOTg4Mg%3D%3D%26mid%3D2247483975%26idx%3D1%26sn%3D95074fa9035aa0e285ed917834646d5f%26scene%3D0
2025-08-15 16:27:43,189 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: requests访问失败，尝试使用Selenium
2025-08-15 16:27:43,189 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 使用Selenium备用方案
2025-08-15 16:27:43,929 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 使用Chrome浏览器（本地ChromeDriver）
2025-08-15 16:27:51,059 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=MzI0MTgwOTg4Mg==&mid=2247483975&idx=1&sn=95074fa9035aa0e285ed917834646d5f&scene=0&poc_token=HIDvnmijLCf_yVScnmzt8M7Exxx2fAE55VD2Jabg
2025-08-15 16:27:51,063 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 页面标题: 为何大行比股份行息差先企稳？
2025-08-15 16:27:51,078 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 检测到验证页面，标识: verify, 验证
2025-08-15 16:27:51,086 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 检测到weui-msg验证页面元素
2025-08-15 16:27:51,093 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: weui-msg元素文本: 
2025-08-15 16:27:51,093 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 开始处理验证页面...
2025-08-15 16:27:54,278 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 第1次验证尝试...
2025-08-15 16:27:54,305 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 检测到文章内容元素: //div[@id="js_content"]
2025-08-15 16:27:54,305 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 已成功跳转到文章页面
2025-08-15 16:27:54,305 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 验证页面处理完成，重新获取页面内容
2025-08-15 16:27:57,336 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: 未找到迁移页面元素: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//div[@class="weui-msg__opr-area"]/p/a[1]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x0000000105221660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x00000001052195d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x0000000104d6a0fc cxxbridge1$string$len + 90068
3   chromedriver                        0x0000000104db14a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x0000000104df2874 cxxbridge1$string$len + 649036
5   chromedriver                        0x0000000104da5790 cxxbridge1$string$len + 333416
6   chromedriver                        0x00000001051e5440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x00000001051e86a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x00000001051c6328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x00000001051e8f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x00000001051b7414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x0000000105209090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x000000010520921c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x0000000105219214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-15 16:27:57,715 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:65] - INFO: Selenium访问成功
