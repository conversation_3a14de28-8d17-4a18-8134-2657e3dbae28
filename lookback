2025-08-14 17:26:26,833 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-14 17:26:26,833 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始改进版批量爬取任务
2025-08-14 17:26:26,833 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输入目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/xlsx_files
2025-08-14 17:26:26,834 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输出目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成
2025-08-14 17:26:26,834 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-14 17:26:26,834 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到 2 个待处理文件
2025-08-14 17:26:26,834 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理文件 1/2: XH-175327274434416.xlsx
2025-08-14 17:26:26,834 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理文件: XH-175327274434416.xlsx
2025-08-14 17:26:26,834 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-14 17:26:26,952 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从临时文件加载数据: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-14 17:26:26,952 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从上次中断的地方继续: 第 451 行
2025-08-14 17:26:26,953 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 451: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247489365&idx=1&sn=f97f0d7d9cf33796c2ca13201554f55d&scene=0
2025-08-14 17:26:26,953 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247489365&idx=1&sn=f97f0d7d9cf33796c2ca13201554f55d&scene=0
2025-08-14 17:26:27,871 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用Chrome浏览器（本地ChromeDriver）
2025-08-14 17:26:31,397 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 1 次检测页面类型
2025-08-14 17:26:31,405 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 检查验证页面时出错: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=139.0.7258.67)
Stacktrace:
0   chromedriver                        0x0000000104d45660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x0000000104d3d5d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x000000010488e0fc cxxbridge1$string$len + 90068
3   chromedriver                        0x0000000104868098 chromedriver + 131224
4   chromedriver                        0x00000001048fd56c cxxbridge1$string$len + 545860
5   chromedriver                        0x00000001049163b4 cxxbridge1$string$len + 647820
6   chromedriver                        0x00000001048c9790 cxxbridge1$string$len + 333416
7   chromedriver                        0x0000000104d09440 cxxbridge1$str$ptr + 2478088
8   chromedriver                        0x0000000104d0c6a8 cxxbridge1$str$ptr + 2490992
9   chromedriver                        0x0000000104cea328 cxxbridge1$str$ptr + 2350832
10  chromedriver                        0x0000000104d0cf64 cxxbridge1$str$ptr + 2493228
11  chromedriver                        0x0000000104cdb414 cxxbridge1$str$ptr + 2289628
12  chromedriver                        0x0000000104d2d090 cxxbridge1$str$ptr + 2624600
13  chromedriver                        0x0000000104d2d21c cxxbridge1$str$ptr + 2624996
14  chromedriver                        0x0000000104d3d214 cxxbridge1$str$ptr + 2690524
15  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
16  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-14 17:26:31,406 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到迁移页面元素: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=139.0.7258.67)
Stacktrace:
0   chromedriver                        0x0000000104d45660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x0000000104d3d5d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x000000010488e0fc cxxbridge1$string$len + 90068
3   chromedriver                        0x0000000104868098 chromedriver + 131224
4   chromedriver                        0x00000001048fd56c cxxbridge1$string$len + 545860
5   chromedriver                        0x00000001049163b4 cxxbridge1$string$len + 647820
6   chromedriver                        0x00000001048c9790 cxxbridge1$string$len + 333416
7   chromedriver                        0x0000000104d09440 cxxbridge1$str$ptr + 2478088
8   chromedriver                        0x0000000104d0c6a8 cxxbridge1$str$ptr + 2490992
9   chromedriver                        0x0000000104cea328 cxxbridge1$str$ptr + 2350832
10  chromedriver                        0x0000000104d0cf64 cxxbridge1$str$ptr + 2493228
11  chromedriver                        0x0000000104cdb414 cxxbridge1$str$ptr + 2289628
12  chromedriver                        0x0000000104d2d090 cxxbridge1$str$ptr + 2624600
13  chromedriver                        0x0000000104d2d21c cxxbridge1$str$ptr + 2624996
14  chromedriver                        0x0000000104d3d214 cxxbridge1$str$ptr + 2690524
15  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
16  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-14 17:26:31,406 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 检查迁移页面时出错: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=139.0.7258.67)
Stacktrace:
0   chromedriver                        0x0000000104d45660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x0000000104d3d5d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x000000010488e0fc cxxbridge1$string$len + 90068
3   chromedriver                        0x0000000104868098 chromedriver + 131224
4   chromedriver                        0x00000001048fd56c cxxbridge1$string$len + 545860
5   chromedriver                        0x00000001049163b4 cxxbridge1$string$len + 647820
6   chromedriver                        0x00000001048c9790 cxxbridge1$string$len + 333416
7   chromedriver                        0x0000000104d09440 cxxbridge1$str$ptr + 2478088
8   chromedriver                        0x0000000104d0c6a8 cxxbridge1$str$ptr + 2490992
9   chromedriver                        0x0000000104cea328 cxxbridge1$str$ptr + 2350832
10  chromedriver                        0x0000000104d0cf64 cxxbridge1$str$ptr + 2493228
11  chromedriver                        0x0000000104cdb414 cxxbridge1$str$ptr + 2289628
12  chromedriver                        0x0000000104d2d090 cxxbridge1$str$ptr + 2624600
13  chromedriver                        0x0000000104d2d21c cxxbridge1$str$ptr + 2624996
14  chromedriver                        0x0000000104d3d214 cxxbridge1$str$ptr + 2690524
15  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
16  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-14 17:26:31,407 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到js_content元素: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=139.0.7258.67)
Stacktrace:
0   chromedriver                        0x0000000104d45660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x0000000104d3d5d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x000000010488e0fc cxxbridge1$string$len + 90068
3   chromedriver                        0x0000000104868098 chromedriver + 131224
4   chromedriver                        0x00000001048fd56c cxxbridge1$string$len + 545860
5   chromedriver                        0x00000001049163b4 cxxbridge1$string$len + 647820
6   chromedriver                        0x00000001048c9790 cxxbridge1$string$len + 333416
7   chromedriver                        0x0000000104d09440 cxxbridge1$str$ptr + 2478088
8   chromedriver                        0x0000000104d0c6a8 cxxbridge1$str$ptr + 2490992
9   chromedriver                        0x0000000104cea328 cxxbridge1$str$ptr + 2350832
10  chromedriver                        0x0000000104d0cf64 cxxbridge1$str$ptr + 2493228
11  chromedriver                        0x0000000104cdb414 cxxbridge1$str$ptr + 2289628
12  chromedriver                        0x0000000104d2d090 cxxbridge1$str$ptr + 2624600
13  chromedriver                        0x0000000104d2d21c cxxbridge1$str$ptr + 2624996
14  chromedriver                        0x0000000104d3d214 cxxbridge1$str$ptr + 2690524
15  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
16  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-14 17:26:31,407 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到文章标题元素: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=139.0.7258.67)
Stacktrace:
0   chromedriver                        0x0000000104d45660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x0000000104d3d5d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x000000010488e0fc cxxbridge1$string$len + 90068
3   chromedriver                        0x0000000104868098 chromedriver + 131224
4   chromedriver                        0x00000001048fd56c cxxbridge1$string$len + 545860
5   chromedriver                        0x00000001049163b4 cxxbridge1$string$len + 647820
6   chromedriver                        0x00000001048c9790 cxxbridge1$string$len + 333416
7   chromedriver                        0x0000000104d09440 cxxbridge1$str$ptr + 2478088
8   chromedriver                        0x0000000104d0c6a8 cxxbridge1$str$ptr + 2490992
9   chromedriver                        0x0000000104cea328 cxxbridge1$str$ptr + 2350832
10  chromedriver                        0x0000000104d0cf64 cxxbridge1$str$ptr + 2493228
11  chromedriver                        0x0000000104cdb414 cxxbridge1$str$ptr + 2289628
12  chromedriver                        0x0000000104d2d090 cxxbridge1$str$ptr + 2624600
13  chromedriver                        0x0000000104d2d21c cxxbridge1$str$ptr + 2624996
14  chromedriver                        0x0000000104d3d214 cxxbridge1$str$ptr + 2690524
15  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
16  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-14 17:26:31,408 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 检查文章页面时出错: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=139.0.7258.67)
Stacktrace:
0   chromedriver                        0x0000000104d45660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x0000000104d3d5d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x000000010488e0fc cxxbridge1$string$len + 90068
3   chromedriver                        0x0000000104868098 chromedriver + 131224
4   chromedriver                        0x00000001048fd56c cxxbridge1$string$len + 545860
5   chromedriver                        0x00000001049163b4 cxxbridge1$string$len + 647820
6   chromedriver                        0x00000001048c9790 cxxbridge1$string$len + 333416
7   chromedriver                        0x0000000104d09440 cxxbridge1$str$ptr + 2478088
8   chromedriver                        0x0000000104d0c6a8 cxxbridge1$str$ptr + 2490992
9   chromedriver                        0x0000000104cea328 cxxbridge1$str$ptr + 2350832
10  chromedriver                        0x0000000104d0cf64 cxxbridge1$str$ptr + 2493228
11  chromedriver                        0x0000000104cdb414 cxxbridge1$str$ptr + 2289628
12  chromedriver                        0x0000000104d2d090 cxxbridge1$str$ptr + 2624600
13  chromedriver                        0x0000000104d2d21c cxxbridge1$str$ptr + 2624996
14  chromedriver                        0x0000000104d3d214 cxxbridge1$str$ptr + 2690524
15  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
16  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-14 17:26:31,408 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: 未知页面类型，尝试等待...
2025-08-14 17:31:58,630 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-14 17:31:58,630 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始改进版批量爬取任务
2025-08-14 17:31:58,631 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输入目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/xlsx_files
2025-08-14 17:31:58,631 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输出目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成
2025-08-14 17:31:58,631 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-14 17:31:58,631 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到 2 个待处理文件
2025-08-14 17:31:58,631 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理文件 1/2: XH-175327274434416.xlsx
2025-08-14 17:31:58,631 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理文件: XH-175327274434416.xlsx
2025-08-14 17:31:58,631 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-14 17:31:58,740 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从临时文件加载数据: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-14 17:31:58,740 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从上次中断的地方继续: 第 451 行
2025-08-14 17:31:58,741 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 451: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247489365&idx=1&sn=f97f0d7d9cf33796c2ca13201554f55d&scene=0
2025-08-14 17:31:58,741 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247489365&idx=1&sn=f97f0d7d9cf33796c2ca13201554f55d&scene=0
2025-08-14 17:31:59,555 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用Chrome浏览器（本地ChromeDriver）
2025-08-14 17:32:02,977 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 1 次检测页面类型
2025-08-14 17:32:02,990 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247489365&idx=1&sn=f97f0d7d9cf33796c2ca13201554f55d&scene=0&poc_token=HA-tnWijMGgshAKrFy_B40rzve5Wfe9mwKCWKTZx
2025-08-14 17:32:02,993 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 账号已迁移
2025-08-14 17:32:03,018 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: weui-msg元素存在但不包含验证文本，可能是文章页面
2025-08-14 17:32:03,034 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面元素
2025-08-14 17:32:03,042 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移标题: 该公众号已迁移
2025-08-14 17:32:03,049 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493762&idx=1&sn=9bcc1ee902ad80bc6b2be1acff7cdceb&source=41#wechat_redirect
2025-08-14 17:32:03,049 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面，处理迁移...
2025-08-14 17:32:03,049 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理迁移页面...
2025-08-14 17:32:03,061 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493762&idx=1&sn=9bcc1ee902ad80bc6b2be1acff7cdceb&source=41#wechat_redirect
2025-08-14 17:32:03,431 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 点击迁移链接成功
2025-08-14 17:32:06,564 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理完成
2025-08-14 17:32:06,566 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理成功，继续检测...
2025-08-14 17:32:08,572 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 2 次检测页面类型
2025-08-14 17:32:08,693 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493762&idx=1&sn=9bcc1ee902ad80bc6b2be1acff7cdceb&source=41&poc_token=HBOtnWijOmCqeJxrBC9_nM25FvCfCFYhCS0f6T3S
2025-08-14 17:32:08,696 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 修复趋势仍在，资金面延续宽松——2022年8月宏观和大类资产配置策略
2025-08-14 17:32:08,711 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到验证页面，标识: verify, 验证
2025-08-14 17:32:08,719 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: weui-msg元素存在但不包含验证文本，可能是文章页面
2025-08-14 17:32:08,726 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到迁移页面元素: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//div[@class="weui-msg__opr-area"]/p/a[1]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x0000000102f3d660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x0000000102f355d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x0000000102a860fc cxxbridge1$string$len + 90068
3   chromedriver                        0x0000000102acd4a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x0000000102b0e874 cxxbridge1$string$len + 649036
5   chromedriver                        0x0000000102ac1790 cxxbridge1$string$len + 333416
6   chromedriver                        0x0000000102f01440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x0000000102f046a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x0000000102ee2328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x0000000102f04f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x0000000102ed3414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x0000000102f25090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x0000000102f2521c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x0000000102f35214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-14 17:32:08,845 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到文章内容元素
2025-08-14 17:32:08,868 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文章内容长度: 4484
2025-08-14 17:32:08,869 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到文章页面，立即提取数据...
2025-08-14 17:32:08,869 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始提取文章数据...
2025-08-14 17:32:08,879 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从meta标签提取到标题: 修复趋势仍在，资金面延续宽松——2022年8月宏观和大类资产配置策略...
2025-08-14 17:32:08,907 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 提取到内容: 4484 字符
2025-08-14 17:32:08,909 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据提取完成
2025-08-14 17:32:08,909 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据提取成功
2025-08-14 17:32:08,998 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 提取到数据 - 标题长度: 32, 内容长度: 4414
2025-08-14 17:32:09,104 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据写入验证成功 (第1次验证) - 标题: 32, 内容: 4414
2025-08-14 17:32:09,104 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ✅ URL 451 处理成功
2025-08-14 17:32:09,171 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-14 17:32:09,209 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 451: 标题长度 32, 内容长度 4414
2025-08-14 17:32:09,210 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 3.8 秒...
2025-08-14 17:32:12,966 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 452: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247491300&idx=1&sn=0006dcb6b980e5c1a95645c54453b394&scene=0
2025-08-14 17:32:12,966 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247491300&idx=1&sn=0006dcb6b980e5c1a95645c54453b394&scene=0
2025-08-14 17:32:13,592 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用Chrome浏览器（本地ChromeDriver）
2025-08-14 17:32:17,226 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 1 次检测页面类型
2025-08-14 17:32:17,316 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247491300&idx=1&sn=0006dcb6b980e5c1a95645c54453b394&scene=0&poc_token=HB2tnWijHyGKIpZWMR6kghI9B5FoBlZC2XYOs-UB
2025-08-14 17:32:17,327 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 账号已迁移
2025-08-14 17:32:17,345 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: weui-msg元素存在但不包含验证文本，可能是文章页面
2025-08-14 17:32:17,364 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面元素
2025-08-14 17:32:17,370 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移标题: 该公众号已迁移
2025-08-14 17:32:17,375 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493838&idx=1&sn=848092592b6541f011b2da6a52b45f80&source=41#wechat_redirect
2025-08-14 17:32:17,375 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面，处理迁移...
2025-08-14 17:32:17,375 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理迁移页面...
2025-08-14 17:32:17,385 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493838&idx=1&sn=848092592b6541f011b2da6a52b45f80&source=41#wechat_redirect
2025-08-14 17:32:17,848 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 点击迁移链接成功
2025-08-14 17:32:23,608 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理完成
2025-08-14 17:32:23,609 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理成功，继续检测...
2025-08-14 17:32:25,612 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 2 次检测页面类型
2025-08-14 17:32:25,713 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493838&idx=1&sn=848092592b6541f011b2da6a52b45f80&source=41&poc_token=HCGtnWij-5X-7F8sK-NlQ6ubiJ03r5Jw-iNlEUtv
2025-08-14 17:32:25,716 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 国信证券获2023年第八届CNABS“金桂奖”
2025-08-14 17:32:25,733 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到验证页面，标识: verify
2025-08-14 17:32:25,741 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: weui-msg元素存在但不包含验证文本，可能是文章页面
2025-08-14 17:32:25,747 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到迁移页面元素: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//div[@class="weui-msg__opr-area"]/p/a[1]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x00000001008ad660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x00000001008a55d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x00000001003f60fc cxxbridge1$string$len + 90068
3   chromedriver                        0x000000010043d4a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x000000010047e874 cxxbridge1$string$len + 649036
5   chromedriver                        0x0000000100431790 cxxbridge1$string$len + 333416
6   chromedriver                        0x0000000100871440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x00000001008746a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x0000000100852328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x0000000100874f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x0000000100843414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x0000000100895090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x000000010089521c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x00000001008a5214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-14 17:32:25,847 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到文章内容元素
2025-08-14 17:32:25,857 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文章内容长度: 737
2025-08-14 17:32:25,857 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到文章页面，立即提取数据...
2025-08-14 17:32:25,857 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始提取文章数据...
2025-08-14 17:32:25,867 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从meta标签提取到标题: 国信证券获2023年第八届CNABS“金桂奖”...
2025-08-14 17:32:25,898 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 提取到内容: 737 字符
2025-08-14 17:32:25,899 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据提取完成
2025-08-14 17:32:25,899 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据提取成功
2025-08-14 17:32:25,988 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 提取到数据 - 标题长度: 21, 内容长度: 714
2025-08-14 17:32:26,094 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据写入验证成功 (第1次验证) - 标题: 21, 内容: 714
2025-08-14 17:32:26,094 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ✅ URL 452 处理成功
2025-08-14 17:32:26,137 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-14 17:32:26,174 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 452: 标题长度 21, 内容长度 714
2025-08-14 17:32:26,175 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 3.5 秒...
2025-08-14 17:32:29,671 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 453: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247490716&idx=1&sn=3bc44539fc5cb7fb196ab8ed6c94f258&scene=0
2025-08-14 17:32:29,671 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247490716&idx=1&sn=3bc44539fc5cb7fb196ab8ed6c94f258&scene=0
2025-08-14 17:32:30,414 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用Chrome浏览器（本地ChromeDriver）
2025-08-14 17:32:33,794 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 1 次检测页面类型
2025-08-14 17:32:33,817 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247490716&idx=1&sn=3bc44539fc5cb7fb196ab8ed6c94f258&scene=0&poc_token=HC6tnWij8dq0qHl2J_uxil4enuX28ovWkV7qYRvq
2025-08-14 17:32:33,822 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 账号已迁移
2025-08-14 17:32:33,842 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: weui-msg元素存在但不包含验证文本，可能是文章页面
2025-08-14 17:32:33,855 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面元素
2025-08-14 17:32:33,859 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移标题: 该公众号已迁移
2025-08-14 17:32:33,862 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493812&idx=1&sn=4ea6e44763ea0fb1e43ef189398597f1&source=41#wechat_redirect
2025-08-14 17:32:33,862 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面，处理迁移...
2025-08-14 17:32:33,862 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理迁移页面...
2025-08-14 17:32:33,868 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493812&idx=1&sn=4ea6e44763ea0fb1e43ef189398597f1&source=41#wechat_redirect
2025-08-14 17:32:34,196 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 点击迁移链接成功
2025-08-14 17:32:37,559 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理完成
2025-08-14 17:32:37,559 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理成功，继续检测...
2025-08-14 17:32:39,562 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 2 次检测页面类型
2025-08-14 17:32:39,664 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493812&idx=1&sn=4ea6e44763ea0fb1e43ef189398597f1&source=41&poc_token=HDKtnWijOcMLbujM1NDg6nBcoeP-VBRFbPuHe33G
2025-08-14 17:32:39,666 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 国信证券关于运用自有资金投资旗下集合资产管理计划的公告
2025-08-14 17:32:39,684 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到验证页面，标识: verify
2025-08-14 17:32:39,692 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: weui-msg元素存在但不包含验证文本，可能是文章页面
2025-08-14 17:32:39,699 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到迁移页面元素: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//div[@class="weui-msg__opr-area"]/p/a[1]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x0000000105301660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x00000001052f95d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x0000000104e4a0fc cxxbridge1$string$len + 90068
3   chromedriver                        0x0000000104e914a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x0000000104ed2874 cxxbridge1$string$len + 649036
5   chromedriver                        0x0000000104e85790 cxxbridge1$string$len + 333416
6   chromedriver                        0x00000001052c5440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x00000001052c86a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x00000001052a6328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x00000001052c8f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x0000000105297414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x00000001052e9090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x00000001052e921c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x00000001052f9214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-14 17:32:39,802 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到文章内容元素
2025-08-14 17:32:39,809 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文章内容长度: 533
2025-08-14 17:32:39,809 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到文章页面，立即提取数据...
2025-08-14 17:32:39,809 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始提取文章数据...
2025-08-14 17:32:39,823 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从meta标签提取到标题: 国信证券关于运用自有资金投资旗下集合资产管理计划的公告...
2025-08-14 17:32:39,847 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 提取到内容: 533 字符
2025-08-14 17:32:39,847 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据提取完成
2025-08-14 17:32:39,847 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据提取成功
2025-08-14 17:32:39,932 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 提取到数据 - 标题长度: 27, 内容长度: 527
2025-08-14 17:32:40,034 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据写入验证成功 (第1次验证) - 标题: 27, 内容: 527
2025-08-14 17:32:40,034 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ✅ URL 453 处理成功
2025-08-14 17:32:40,078 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-14 17:32:40,116 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 453: 标题长度 27, 内容长度 527
2025-08-14 17:32:40,116 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 3.8 秒...
2025-08-14 17:46:19,058 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用高级方法访问URL: https://mp.weixin.qq.com/s?__biz=Mzk0OTYwMjk4Nw==&mid=2247485670&idx=1&sn=2d9b0a41233acb7b97dcd6d16f2408ec&chksm=c3549722f4231e342148686a0b4f4847e0706051cfc384731c9d7909f031c1e9a660b4104c5a#rd
2025-08-14 17:46:19,711 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用编码: utf-8
2025-08-14 17:46:19,747 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 成功获取标题: 【热线攻坚】区房管局联合街道开展“12345”热线住宅物业诉求分析研究专题工作会...
2025-08-14 17:46:19,790 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 成功获取发布时间: 2024-03-08 15:39:00
2025-08-14 17:46:19,791 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 成功获取内容，长度: 745
2025-08-14 17:46:19,791 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 成功获取图片数量: 4
2025-08-14 17:46:19,792 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 成功获取数据 - 标题: 【热线攻坚】区房管局联合街道开展“12345”热线住宅物业诉求分析研究专题工作会..., 内容长度: 745
2025-08-14 17:46:29,797 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: mq_push_error:
2025-08-14 17:46:29,797 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: result:{"taskSid": "test_improved_1", "itemSid": "test_improved_1", "dataType": "lookback_wechat", "publishTime": "2024-03-08 15:39:00", "title": "【热线攻坚】区房管局联合街道开展“12345”热线住宅物业诉求分析研究专题工作会", "content": "为贯彻落实区委区政府关于“12345”市民服务热线工作的要求，2月29日下午，区房管局会同平凉、定海、大桥、江浦街道联合召开“12345”热线住宅物业诉求分析研究专题工作会。街道城运中心和城建中心负责人及工作人员、区热线办及区房管局参加会议。 会上，平凉、定海、大桥、江浦街道结合实际，逐一对近期不满意工单作情况分析，交流讨论“12345”热线中住宅物业类工单存在的问题、处置的难点以及后续工作措施。 房管局针对反映集中的物业管理类问题工单进行了一一分析，并给出了具体解决方案的建议。会议邀请区热线办对热线工单处置和服务理念作了培训，并对办理中的疑难问题做了进一步分析，提出了指导意见。 住宅物业类问题的投诉量名列前茅，如何有效调处物业管理矛盾纠纷、解决诉求人的实际困难，是目前各街道热线处置工作的重点、难点和痛点。区房管局与各街道紧密配合，条块协同，从精准基层治理到行业整体提升全向发力。 进一步加强监管， 以规范本区住宅物业服务活动为重点，结合物业服务规范达标“双月行动”，深入开展行之有效的“削峰斩尾”行动，深入开展检查，严查不规范行为，切实增强物业服务企业规范履约意识，树立良好形象。 进一步补齐短板， 针对群众反映强烈的物业安保、停车管理、业委会运作、小区垃圾清运等热线问题，特别是重新交办类案件、重复投诉类案件，寻找问题根源，聚焦重点小区、重点领域，切实采取有效措施，做好预防治理。 进一步沟通协作， 加深条块结合，逐步形成上下联动、齐抓共管、标本兼治的工作格局，力求高效解决住宅物业治理难题，为民纾困解难助发展，切实提升群众的获得感、幸福感和满意度。 会后，区房管局将印发《杨浦区加强“12345”市民服务热线办理提升物业管理质效工作方案》。 信息来源于上海杨房宣传微信公众号", "imgs": "https://mmbiz.qpic.cn/sz_mmbiz_gif/HPyibrvwOCjiaVCJhh6C3fnfoMqRgah0EdwuofUzJGef8cltfBfeP7wicrIQfUAanaouj6pf4uo3wUH4QLAQibbbVQ/640?wx_fmt=gif&from=appmsg,https://mmbiz.qpic.cn/sz_mmbiz_jpg/HPyibrvwOCjia51Gpdzonpm5A9dbOfe1ia1vW04Qcff3IJDjn00qpo5JGlf3Y2OCfxGw0Tv8kEcZV6q5NDKuhGsIw/640?wx_fmt=jpeg&from=appmsg,https://mmbiz.qpic.cn/sz_mmbiz_gif/HPyibrvwOCjia51Gpdzonpm5A9dbOfe1ia1rR3XSbKednyLn1s2V7TGRnic9hu891ZCBSNW5VzLeC6xPaxxibTBofFg/640?wx_fmt=gif&from=appmsg,https://mmbiz.qpic.cn/sz_mmbiz_jpg/HPyibrvwOCjiaVCJhh6C3fnfoMqRgah0EdJZnYxO7XUtvDIEhSsia34ECxWO3Gp0LXYHljDIW5O4Xz7EdHlicBAtJw/640?wx_fmt=jpeg&from=appmsg"}
2025-08-14 17:46:43,803 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用高级方法访问URL: https://mp.weixin.qq.com/s?__biz=MzIzOTEwNTI1NQ==&mid=2247507915&idx=1&sn=79351567dbbd9b2ac184cdc6b0b7c79b&scene=0
2025-08-14 17:46:44,068 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用编码: utf-8
2025-08-14 17:46:44,069 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: 被重定向到验证页面: https://mp.weixin.qq.com/mp/wappoc_appmsgcaptcha?poc_token=HISwnWij4Eev0YQsg_Uq7W8cl9diT0Uk6TJoFTC5&target_url=https%3A%2F%2Fmp.weixin.qq.com%2Fs%3F__biz%3DMzIzOTEwNTI1NQ%3D%3D%26mid%3D2247507915%26idx%3D1%26sn%3D79351567dbbd9b2ac184cdc6b0b7c79b%26scene%3D0
2025-08-14 17:46:44,072 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: 高级方法失败，等待后重试: http://mp.weixin.qq.com/s?__biz=MzIzOTEwNTI1NQ==&mid=2247507915&idx=1&sn=79351567dbbd9b2ac184cdc6b0b7c79b&scene=0
2025-08-14 17:47:26,077 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用高级方法访问URL: https://mp.weixin.qq.com/s?__biz=MzIzOTEwNTI1NQ==&mid=2247507915&idx=1&sn=79351567dbbd9b2ac184cdc6b0b7c79b&scene=0
2025-08-14 17:47:26,353 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用编码: utf-8
2025-08-14 17:47:26,353 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: 被重定向到验证页面: https://mp.weixin.qq.com/mp/wappoc_appmsgcaptcha?poc_token=HK6wnWijBuY8v8HaMgctNL7vyP018y3Kr31kRP2r&target_url=https%3A%2F%2Fmp.weixin.qq.com%2Fs%3F__biz%3DMzIzOTEwNTI1NQ%3D%3D%26mid%3D2247507915%26idx%3D1%26sn%3D79351567dbbd9b2ac184cdc6b0b7c79b%26scene%3D0
2025-08-14 17:47:26,356 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: 高级方法失败，等待后重试: http://mp.weixin.qq.com/s?__biz=MzIzOTEwNTI1NQ==&mid=2247507915&idx=1&sn=79351567dbbd9b2ac184cdc6b0b7c79b&scene=0
2025-08-14 17:48:01,360 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用高级方法访问URL: https://mp.weixin.qq.com/s?__biz=MzIzOTEwNTI1NQ==&mid=2247507915&idx=1&sn=79351567dbbd9b2ac184cdc6b0b7c79b&scene=0
2025-08-14 17:48:01,703 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用编码: utf-8
2025-08-14 17:48:01,703 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: 被重定向到验证页面: https://mp.weixin.qq.com/mp/wappoc_appmsgcaptcha?poc_token=HNGwnWij-1lLYM3w8FGbYCsg_ASmoRJcVd5uDuIa&target_url=https%3A%2F%2Fmp.weixin.qq.com%2Fs%3F__biz%3DMzIzOTEwNTI1NQ%3D%3D%26mid%3D2247507915%26idx%3D1%26sn%3D79351567dbbd9b2ac184cdc6b0b7c79b%26scene%3D0
2025-08-14 17:48:01,705 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: 高级方法失败，等待后重试: http://mp.weixin.qq.com/s?__biz=MzIzOTEwNTI1NQ==&mid=2247507915&idx=1&sn=79351567dbbd9b2ac184cdc6b0b7c79b&scene=0
2025-08-14 17:48:31,710 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用高级方法访问URL: https://mp.weixin.qq.com/s?__biz=MzIzOTEwNTI1NQ==&mid=2247507915&idx=1&sn=79351567dbbd9b2ac184cdc6b0b7c79b&scene=0
2025-08-14 17:48:32,108 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用编码: utf-8
2025-08-14 17:48:32,108 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: 被重定向到验证页面: https://mp.weixin.qq.com/mp/wappoc_appmsgcaptcha?poc_token=HPCwnWijqAGB2QecgXD1KLvkrFA9LiIFLjriTUf3&target_url=https%3A%2F%2Fmp.weixin.qq.com%2Fs%3F__biz%3DMzIzOTEwNTI1NQ%3D%3D%26mid%3D2247507915%26idx%3D1%26sn%3D79351567dbbd9b2ac184cdc6b0b7c79b%26scene%3D0
2025-08-14 17:48:32,110 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: 高级方法失败，等待后重试: http://mp.weixin.qq.com/s?__biz=MzIzOTEwNTI1NQ==&mid=2247507915&idx=1&sn=79351567dbbd9b2ac184cdc6b0b7c79b&scene=0
2025-08-14 17:49:29,121 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用高级方法访问URL: https://mp.weixin.qq.com/s?__biz=MzIzOTEwNTI1NQ==&mid=2247507915&idx=1&sn=79351567dbbd9b2ac184cdc6b0b7c79b&scene=0
2025-08-14 17:49:29,388 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用编码: utf-8
2025-08-14 17:49:29,389 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: 被重定向到验证页面: https://mp.weixin.qq.com/mp/wappoc_appmsgcaptcha?poc_token=HCmxnWijrYhJz29A9V-xSq8P6G8RXSYYRYxtyzC5&target_url=https%3A%2F%2Fmp.weixin.qq.com%2Fs%3F__biz%3DMzIzOTEwNTI1NQ%3D%3D%26mid%3D2247507915%26idx%3D1%26sn%3D79351567dbbd9b2ac184cdc6b0b7c79b%26scene%3D0
2025-08-14 17:49:29,390 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: 高级方法失败，等待后重试: http://mp.weixin.qq.com/s?__biz=MzIzOTEwNTI1NQ==&mid=2247507915&idx=1&sn=79351567dbbd9b2ac184cdc6b0b7c79b&scene=0
2025-08-14 17:53:21,169 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用高级方法访问URL: https://mp.weixin.qq.com/s?__biz=Mzk0OTYwMjk4Nw==&mid=2247485670&idx=1&sn=2d9b0a41233acb7b97dcd6d16f2408ec&chksm=c3549722f4231e342148686a0b4f4847e0706051cfc384731c9d7909f031c1e9a660b4104c5a#rd
2025-08-14 17:53:21,807 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用编码: utf-8
2025-08-14 17:53:21,835 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 成功获取标题: 【热线攻坚】区房管局联合街道开展“12345”热线住宅物业诉求分析研究专题工作会...
2025-08-14 17:53:21,878 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 成功获取发布时间: 2024-03-08 15:39:00
2025-08-14 17:53:21,878 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 成功获取内容，长度: 745
2025-08-14 17:53:21,878 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 成功获取图片数量: 4
2025-08-14 17:53:21,880 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 成功获取数据 - 标题: 【热线攻坚】区房管局联合街道开展“12345”热线住宅物业诉求分析研究专题工作会..., 内容长度: 745
2025-08-14 17:54:02,894 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用高级方法访问URL: https://mp.weixin.qq.com/s?__biz=Mzk0OTYwMjk4Nw==&mid=2247485670&idx=1&sn=2d9b0a41233acb7b97dcd6d16f2408ec&chksm=c3549722f4231e342148686a0b4f4847e0706051cfc384731c9d7909f031c1e9a660b4104c5a#rd
2025-08-14 17:54:03,465 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用编码: utf-8
2025-08-14 17:54:03,493 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 成功获取标题: 【热线攻坚】区房管局联合街道开展“12345”热线住宅物业诉求分析研究专题工作会...
2025-08-14 17:54:03,535 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 成功获取发布时间: 2024-03-08 15:39:00
2025-08-14 17:54:03,536 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 成功获取内容，长度: 745
2025-08-14 17:54:03,536 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 成功获取图片数量: 4
2025-08-14 17:54:03,537 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 成功获取数据 - 标题: 【热线攻坚】区房管局联合街道开展“12345”热线住宅物业诉求分析研究专题工作会..., 内容长度: 745
2025-08-14 17:54:13,540 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: mq_push_error:
2025-08-14 17:54:13,540 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: result:{"taskSid": "test_improved_1", "itemSid": "test_improved_1", "dataType": "lookback_wechat", "publishTime": "2024-03-08 15:39:00", "title": "【热线攻坚】区房管局联合街道开展“12345”热线住宅物业诉求分析研究专题工作会", "content": "为贯彻落实区委区政府关于“12345”市民服务热线工作的要求，2月29日下午，区房管局会同平凉、定海、大桥、江浦街道联合召开“12345”热线住宅物业诉求分析研究专题工作会。街道城运中心和城建中心负责人及工作人员、区热线办及区房管局参加会议。 会上，平凉、定海、大桥、江浦街道结合实际，逐一对近期不满意工单作情况分析，交流讨论“12345”热线中住宅物业类工单存在的问题、处置的难点以及后续工作措施。 房管局针对反映集中的物业管理类问题工单进行了一一分析，并给出了具体解决方案的建议。会议邀请区热线办对热线工单处置和服务理念作了培训，并对办理中的疑难问题做了进一步分析，提出了指导意见。 住宅物业类问题的投诉量名列前茅，如何有效调处物业管理矛盾纠纷、解决诉求人的实际困难，是目前各街道热线处置工作的重点、难点和痛点。区房管局与各街道紧密配合，条块协同，从精准基层治理到行业整体提升全向发力。 进一步加强监管， 以规范本区住宅物业服务活动为重点，结合物业服务规范达标“双月行动”，深入开展行之有效的“削峰斩尾”行动，深入开展检查，严查不规范行为，切实增强物业服务企业规范履约意识，树立良好形象。 进一步补齐短板， 针对群众反映强烈的物业安保、停车管理、业委会运作、小区垃圾清运等热线问题，特别是重新交办类案件、重复投诉类案件，寻找问题根源，聚焦重点小区、重点领域，切实采取有效措施，做好预防治理。 进一步沟通协作， 加深条块结合，逐步形成上下联动、齐抓共管、标本兼治的工作格局，力求高效解决住宅物业治理难题，为民纾困解难助发展，切实提升群众的获得感、幸福感和满意度。 会后，区房管局将印发《杨浦区加强“12345”市民服务热线办理提升物业管理质效工作方案》。 信息来源于上海杨房宣传微信公众号", "imgs": "https://mmbiz.qpic.cn/sz_mmbiz_gif/HPyibrvwOCjiaVCJhh6C3fnfoMqRgah0EdwuofUzJGef8cltfBfeP7wicrIQfUAanaouj6pf4uo3wUH4QLAQibbbVQ/640?wx_fmt=gif&from=appmsg,https://mmbiz.qpic.cn/sz_mmbiz_jpg/HPyibrvwOCjia51Gpdzonpm5A9dbOfe1ia1vW04Qcff3IJDjn00qpo5JGlf3Y2OCfxGw0Tv8kEcZV6q5NDKuhGsIw/640?wx_fmt=jpeg&from=appmsg,https://mmbiz.qpic.cn/sz_mmbiz_gif/HPyibrvwOCjia51Gpdzonpm5A9dbOfe1ia1rR3XSbKednyLn1s2V7TGRnic9hu891ZCBSNW5VzLeC6xPaxxibTBofFg/640?wx_fmt=gif&from=appmsg,https://mmbiz.qpic.cn/sz_mmbiz_jpg/HPyibrvwOCjiaVCJhh6C3fnfoMqRgah0EdJZnYxO7XUtvDIEhSsia34ECxWO3Gp0LXYHljDIW5O4Xz7EdHlicBAtJw/640?wx_fmt=jpeg&from=appmsg"}
2025-08-14 17:54:32,544 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用高级方法访问URL: https://mp.weixin.qq.com/s?__biz=MzIzOTEwNTI1NQ==&mid=2247507915&idx=1&sn=79351567dbbd9b2ac184cdc6b0b7c79b&scene=0
2025-08-14 17:54:33,094 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用编码: utf-8
2025-08-14 17:54:33,094 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: 被重定向到验证页面: https://mp.weixin.qq.com/mp/wappoc_appmsgcaptcha?poc_token=HFiynWijNmplxCHvLHENxuV3mx9BhWi0Ueugs_37&target_url=https%3A%2F%2Fmp.weixin.qq.com%2Fs%3F__biz%3DMzIzOTEwNTI1NQ%3D%3D%26mid%3D2247507915%26idx%3D1%26sn%3D79351567dbbd9b2ac184cdc6b0b7c79b%26scene%3D0
2025-08-14 17:54:33,096 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: 高级方法失败，等待后重试: http://mp.weixin.qq.com/s?__biz=MzIzOTEwNTI1NQ==&mid=2247507915&idx=1&sn=79351567dbbd9b2ac184cdc6b0b7c79b&scene=0
