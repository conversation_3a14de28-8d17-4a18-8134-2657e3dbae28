# -*- coding: utf-8 -*-
import datetime
import logging
import time
from logging import handlers

class Log(object):
    level_relations = {
        'debug': logging.DEBUG,
        'info': logging.INFO,
        'warning': logging.WARNING,
        'error': logging.ERROR,
        'crit': logging.CRITICAL
    }
    logger = logging.getLogger('user')
    save_handler = None


    @classmethod
    def init(cls, printflag=False, level='info', when='MIDNIGHT', backCount=7, fmt='%(asctime)s - %(pathname)s[line:%(lineno)d] - %(levelname)s: %(message)s'):
        try:
            format_str = logging.Formatter(fmt)
            cls.logger.setLevel(cls.level_relations.get(level))
            if printflag:
                console_handler = logging.StreamHandler()
                console_handler.setLevel(logging.INFO)
                console_handler.setFormatter(format_str)
                cls.logger.addHandler(console_handler)
            # 往文件里写入#指定间隔时间自动生成文件的处理器
            save_handler = handlers.TimedRotatingFileHandler("lookback", when=when, interval=1, backupCount=backCount, encoding='utf-8', delay=True)
            save_handler.suffix = "%Y-%m-%d.log"
            save_handler.setFormatter(format_str)
            cls.logger.addHandler(save_handler)
            cls.save_handler = save_handler
        except Exception as e:
            logging.error(e.__str__())

    @classmethod
    def setLevel(cls, level):
        cls.logger.setLevel(level)

    @classmethod
    def debug(cls, msg, *args, **kwargs):
        try:
            cls.logger.debug(msg, *args, **kwargs)
            if cls.save_handler:
                cls.save_handler.flush()
        except PermissionError:
            time.sleep(1)
            cls.debug(msg, *args, **kwargs)

    @classmethod
    def error(cls, msg, *args, **kwargs):
        try:
            cls.logger.error(msg, *args, **kwargs)
            if cls.save_handler:
                cls.save_handler.flush()
        except PermissionError:
            time.sleep(1)
            cls.error(msg, *args, **kwargs)

    @classmethod
    def info(cls, msg, *args, **kwargs):
        try:
            cls.logger.info(msg, *args, **kwargs)
            # cls.save_handler.flush()
        except PermissionError:
            time.sleep(1)
            cls.info(msg, *args, **kwargs)

    @classmethod
    def warning(cls, msg, *args, **kwargs):
        try:
            cls.logger.warning(msg, *args, **kwargs)
            if cls.save_handler:
                cls.save_handler.flush()
        except PermissionError:
            time.sleep(1)
            cls.warning(msg, *args, **kwargs)

    @classmethod
    def exception(cls, msg, *args, exc_info=True, **kwargs):
        cls.logger.exception(msg, *args, exc_info, **kwargs)

    @classmethod
    def critical(cls, msg, *args, **kwargs):
        cls.logger.critical(msg, *args, **kwargs)

    @classmethod
    def log(cls, level, msg, *args, **kwargs):
        try:
            cls.logger.log(level, msg, *args, **kwargs)
        except PermissionError:
            time.sleep(1)
            cls.log(level, msg, *args, **kwargs)

    @classmethod
    def findCaller(cls, stack_info=False, stacklevel=1):
        cls.logger.findCaller(stack_info, stacklevel)

    @classmethod
    def makeRecord(cls, name, level, fn, lno, msg, args, exc_info,
                   func=None, extra=None, sinfo=None):
        cls.logger.makeRecord(name, level, fn, lno, msg, args, exc_info,
                   func, extra, sinfo)

    @classmethod
    def handle(cls, record):
        cls.logger.handle(record)



