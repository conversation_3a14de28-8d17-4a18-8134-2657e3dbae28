# -*- coding: utf-8 -*-
from typing import Optional

from src.utils import PropertiesUtils


class Param:
    port: Optional[int] = PropertiesUtils.Utils.get_config('service', 'port')
    mq_host: Optional[str] = PropertiesUtils.Utils.get_config('RabbitMQ', 'host')
    mq_port: Optional[str] = PropertiesUtils.Utils.get_config('RabbitMQ', 'port')
    mq_username: Optional[str] = PropertiesUtils.Utils.get_config('RabbitMQ', 'username')
    mq_password: Optional[str] = PropertiesUtils.Utils.get_config('RabbitMQ', 'password')
    mq_task_exchange: Optional[str] = PropertiesUtils.Utils.get_config('RabbitMQ', 'task-exchange')
    mq_task_queue_webo: Optional[str] = PropertiesUtils.Utils.get_config('RabbitMQ', 'task-queue-webo')
    mq_task_queue_wechat: Optional[str] = PropertiesUtils.Utils.get_config('RabbitMQ', 'task-queue-wechat')
    mq_result_exchange: Optional[str] = PropertiesUtils.Utils.get_config('RabbitMQ', 'result-exchange')
    mq_result_queue_webo: Optional[str] = PropertiesUtils.Utils.get_config('RabbitMQ', 'result-queue-webo')
    mq_result_queue_wechat: Optional[str] = PropertiesUtils.Utils.get_config('RabbitMQ', 'result-queue-wechat')
    mq_webo_route_key: Optional[str] = PropertiesUtils.Utils.get_config('RabbitMQ', 'webo-route-key')
    mq_wechat_route_key: Optional[str] = PropertiesUtils.Utils.get_config('RabbitMQ', 'wechat-route-key')