# -*- coding: utf-8 -*-
import os
import sys
import time
import random
import requests
import json
import re
import urllib.parse
from urllib.parse import urlparse, parse_qs
from typing import Optional, Dict, Any

import requests
from bs4 import BeautifulSoup
from selenium.webdriver.common.by import By

from src.log import Logger
from src.utils import CommentUtils


class WechatLookBackAdvanced:
    
    def __init__(self):
        self.session = requests.Session()
        self.proxy_list = []
        self.current_proxy_index = 0
        self.request_count = 0
        self.last_request_time = 0
        self.setup_session()
        self.load_proxies()
    
    def setup_session(self):
        """设置更真实的请求会话"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (<PERSON>HT<PERSON>, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ]
        
        self.session.headers.update({
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"'
        })
    
    def load_proxies(self):
        """加载代理列表"""
        try:
            from config.proxy_config import PROXY_LIST, ENABLE_PROXY
            if ENABLE_PROXY and PROXY_LIST:
                self.proxy_list = PROXY_LIST
                Logger.Log.info(f"已加载 {len(self.proxy_list)} 个代理")
            else:
                Logger.Log.info("代理未启用或代理列表为空")
        except ImportError:
            Logger.Log.warning("代理配置文件不存在，将不使用代理")
    
    def setup_proxy_for_requests(self, proxy):
        """为requests设置代理"""
        if proxy:
            self.session.proxies = {
                'http': proxy,
                'https': proxy
            }
            Logger.Log.info(f"使用代理: {proxy}")
        else:
            self.session.proxies = {}
            Logger.Log.info("不使用代理")
    
    def get_next_proxy(self):
        """获取下一个代理"""
        if not self.proxy_list:
            return None
        
        proxy = self.proxy_list[self.current_proxy_index]
        self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxy_list)
        return proxy
    
    def generate_fingerprint(self):
        """生成浏览器指纹"""
        screen_resolutions = ['1920x1080', '1366x768', '1440x900', '1536x864']
        color_depths = [24, 32]
        timezones = ['Asia/Shanghai', 'Asia/Hong_Kong']
        
        fingerprint = {
            'screen': random.choice(screen_resolutions),
            'colorDepth': random.choice(color_depths),
            'timezone': random.choice(timezones),
            'language': 'zh-CN',
            'platform': 'Win32'
        }
        
        return fingerprint
    
    def add_wechat_headers(self, url):
        """添加微信特定的请求头"""
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)
        
        # 添加微信特定的头部
        additional_headers = {
            'Referer': 'https://mp.weixin.qq.com/',
            'Origin': 'https://mp.weixin.qq.com',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        # 如果有特定的微信参数，添加到头部
        if '__biz' in query_params:
            additional_headers['X-Wechat-Biz'] = query_params['__biz'][0]
        
        self.session.headers.update(additional_headers)
    
    def simulate_human_behavior(self):
        """模拟人类行为"""
        # 随机延迟
        time.sleep(random.uniform(1, 3))
        
        # 随机更新User-Agent
        if random.random() < 0.3:
            user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ]
            self.session.headers['User-Agent'] = random.choice(user_agents)
        
        # 控制请求频率
        current_time = time.time()
        if current_time - self.last_request_time < 15:
            sleep_time = 15 - (current_time - self.last_request_time)
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    def handle_network_errors(self, e):
        """处理网络错误"""
        error_msg = str(e).lower()
        
        if 'timeout' in error_msg:
            Logger.Log.warning("请求超时，可能需要增加超时时间")
            return 'timeout'
        elif 'connection' in error_msg:
            Logger.Log.warning("连接错误，可能是网络问题")
            return 'connection'
        elif 'ssl' in error_msg:
            Logger.Log.warning("SSL证书错误")
            return 'ssl'
        elif '403' in error_msg or 'forbidden' in error_msg:
            Logger.Log.warning("访问被禁止，可能需要更换IP或User-Agent")
            return 'forbidden'
        elif '404' in error_msg:
            Logger.Log.warning("页面不存在")
            return 'not_found'
        elif '429' in error_msg or 'too many requests' in error_msg:
            Logger.Log.warning("请求过于频繁，需要增加延迟")
            return 'rate_limit'
        else:
            Logger.Log.error(f"未知网络错误: {str(e)}")
            return 'unknown'
    
    def fix_encoding(self, text):
        """修复编码问题 - 简化版本"""
        if not text:
            return text
        
        try:
            # 如果已经是正确的中文字符串，直接返回
            if isinstance(text, str) and any('\u4e00' <= char <= '\u9fff' for char in text):
                return text
            
            # 如果是字节串，尝试解码
            if isinstance(text, bytes):
                try:
                    return text.decode('utf-8')
                except:
                    try:
                        return text.decode('gbk')
                    except:
                        return text.decode('utf-8', errors='ignore')
            
            # 如果是字符串，尝试HTML实体解码
            elif isinstance(text, str):
                try:
                    import html
                    decoded_text = html.unescape(text)
                    if decoded_text != text:
                        return decoded_text
                except:
                    pass
                
                return text
                
        except Exception as e:
            Logger.Log.error(f"编码修复失败: {str(e)}")
            return str(text) if text else ""
        
        return text
    
    def clean_text(self, text):
        """清理文本内容"""
        if not text:
            return ""
        
        try:
            # 修复编码
            text = self.fix_encoding(text)
            
            # 处理HTML实体
            try:
                import html
                text = html.unescape(text)
            except:
                pass
            
            # 移除多余的空白字符
            text = re.sub(r'\s+', ' ', text.strip())
            
            # 移除控制字符
            text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)
            
            # 保留中文、英文、数字、标点符号等有用字符
            text = re.sub(r'[^\w\s\u4e00-\u9fff\u3000-\u303f\uff00-\uffef\-\.\,\!\?\(\)\[\]\{\}\:\;\'\"\@\#\$\%\^\&\*\+\=\|\~`]', '', text)
            
            # 再次清理多余空白
            text = re.sub(r'\s+', ' ', text.strip())
            
            return text
        except Exception as e:
            Logger.Log.error(f"文本清理失败: {str(e)}")
            return str(text) if text else ""
    
    def validate_data(self, data):
        """验证获取的数据"""
        if not data:
            return False
        
        # 检查必要字段
        required_fields = ['title', 'content']
        for field in required_fields:
            if field not in data or not data[field]:
                Logger.Log.warning(f"缺少必要字段: {field}")
                return False
        
        # 检查内容长度
        if len(data.get('content', '')) < 10:
            Logger.Log.warning("内容太短，可能获取失败")
            return False
        
        # 检查标题长度
        if len(data.get('title', '')) < 2:
            Logger.Log.warning("标题太短，可能获取失败")
            return False
        
        return True
    
    def get_data_with_advanced_techniques(self, url):
        """使用高级技术获取数据"""
        try:
            # 添加微信特定头部
            self.add_wechat_headers(url)
            
            # 模拟人类行为
            self.simulate_human_behavior()
            
            # 获取代理并设置
            proxy = self.get_next_proxy()
            self.setup_proxy_for_requests(proxy)
            
            # 发送请求
            response = self.session.get(
                url, 
                timeout=30, 
                allow_redirects=True
            )
            response.raise_for_status()
            
            # 处理编码
            try:
                response.encoding = response.apparent_encoding
            except:
                response.encoding = 'utf-8'
            
            # 强制修复响应编码
            try:
                # 尝试多种编码方式处理响应内容
                encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'big5']
                for encoding in encodings_to_try:
                    try:
                        # 重新解码响应内容
                        decoded_content = response.content.decode(encoding)
                        # 检查是否包含中文字符
                        if any('\u4e00' <= char <= '\u9fff' for char in decoded_content):
                            response.encoding = encoding
                            Logger.Log.info(f"响应编码修复为: {encoding}")
                            break
                    except:
                        continue
            except:
                pass
            
            # 检查是否被重定向到验证页面
            if 'verify' in response.url.lower() or 'captcha' in response.url.lower():
                Logger.Log.warning(f"被重定向到验证页面: {response.url}")
                return None
            
            # 检查页面内容是否包含验证相关文本
            response_text = response.text.lower()
            verification_keywords = ['验证', '安全验证', '人机验证', '请完成验证', '点击验证', '滑动验证', '点选验证', 'verify', 'captcha']
            if any(keyword in response_text for keyword in verification_keywords):
                Logger.Log.warning("页面包含验证内容，需要Selenium处理")
                return None
            
            soup = BeautifulSoup(response.text, "html.parser")
            
            # 检查是否被删除
            if soup.find(attrs={'class': 'weui-msg__title warn'}) is not None and \
                    '该内容已被发布者删除' in soup.find(attrs={'class': 'weui-msg__title warn'}).text:
                return {'content': '该内容已被发布者删除'}
            
            # 检查是否被限制访问
            if soup.find(text=re.compile(r'访问过于频繁|访问受限|验证码')):
                Logger.Log.warning("访问被限制，需要验证")
                return None
            
            # 获取标题
            title = ''
            try:
                title_element = soup.find(attrs={'property': 'og:title'})
                if title_element:
                    title = title_element.attrs['content']
                else:
                    # 备用方案：从页面标题获取
                    title_element = soup.find('title')
                    if title_element:
                        title = title_element.text.strip()
                
                # 清理标题
                title = self.clean_text(title)
            except Exception as e:
                Logger.Log.error(f"获取标题失败: {str(e)}")
                title = ''
            
            # 获取发布时间
            publish_time = ''
            try:
                # 多种时间格式的正则表达式
                time_patterns = [
                    r'var createTime\s*=\s*[\'"]([^\'"]+)[\'"]',
                    r'publish_time[\'"]?\s*:\s*[\'"]([^\'"]+)[\'"]',
                    r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})',
                    r'(\d{4}年\d{1,2}月\d{1,2}日)'
                ]
                
                scripts = soup.findAll('script')
                for script in scripts:
                    script_text = script.text
                    for pattern in time_patterns:
                        match = re.search(pattern, script_text)
                        if match:
                            try:
                                publish_time = CommentUtils.Utils.date_format(match.group(1))
                                break
                            except:
                                continue
                    if publish_time:
                        break
            except Exception as e:
                Logger.Log.error(f"获取发布时间失败: {str(e)}")
                publish_time = ''
            
            # 获取正文内容
            content = ''
            try:
                content_element = soup.find('div', {'id': 'js_content'})
                if content_element:
                    # 移除脚本和样式标签
                    for script in content_element(["script", "style"]):
                        script.decompose()
                    content = content_element.get_text(separator='\n', strip=True)
                    # 清理内容
                    content = self.clean_text(content)
            except Exception as e:
                Logger.Log.error(f"获取正文内容失败: {str(e)}")
                content = ''
            
            # 获取图片
            imgs = ''
            try:
                if content_element:
                    images = content_element.findAll('img')
                    img_urls = []
                    for img in images:
                        src = img.attrs.get('data-src') or img.attrs.get('src')
                        if src and not src.startswith('data:'):
                            img_urls.append(src)
                    imgs = ','.join(img_urls)
            except Exception as e:
                Logger.Log.error(f"获取图片失败: {str(e)}")
                imgs = ''
            
            data = {
                'title': title,
                'publishTime': publish_time,
                'content': content,
                'imgs': imgs
            }
            
            # 验证数据
            if not self.validate_data(data):
                Logger.Log.warning("数据验证失败，可能获取不完整")
            
            return data
            
        except Exception as e:
            error_type = self.handle_network_errors(e)
            Logger.Log.error(f"高级获取方法失败: {str(e)} (错误类型: {error_type})")
            return None
    
    def get_data_with_selenium_fallback(self, url):
        """Selenium备用方案"""
        try:
            from selenium import webdriver
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.common.action_chains import ActionChains
            
            Logger.Log.info("使用Selenium备用方案")
             
             # 使用Chrome浏览器
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
             
            chrome_options = Options()
             # chrome_options.add_argument('--headless')  # 暂时不使用headless模式便于调试
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
             
             # 添加更多反检测参数
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
             
             # 添加更真实的User-Agent
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
             
             # 禁用WebDriver检测
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')
            
            # 添加代理支持
            proxy = self.get_next_proxy()
            if proxy:
                chrome_options.add_argument(f'--proxy-server={proxy}')
                Logger.Log.info(f"Selenium使用代理: {proxy}")
            
            # 随机User-Agent
            user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36'
            ]
            chrome_options.add_argument(f'--user-agent={random.choice(user_agents)}')
            
            # 反检测参数
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 使用当前目录的ChromeDriver
            import os
            chromedriver_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'chromedriver.exe')
            # 指定Chrome浏览器路径
            chrome_options.binary_location = r"C:\Program Files (x86)\Qoom\Chrome\chrome.exe"
            # Selenium 4.x版本使用Service
            from selenium.webdriver.chrome.service import Service
            service = Service(chromedriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
            Logger.Log.info("使用Chrome浏览器（本地ChromeDriver）")
            
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            try:
                driver.get(url)
                WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                time.sleep(random.uniform(2, 5))
                
                # 检查并处理验证页面
                if self.handle_verification_page(driver):
                    Logger.Log.info("验证页面处理完成，重新获取页面内容")
                    time.sleep(random.uniform(3, 6))
                
                # 检查并处理公众号迁移页面
                if self.is_migration_page(driver):
                    Logger.Log.info("检测到公众号迁移页面，尝试点击迁移链接...")
                    if self.handle_migration_page(driver):
                        Logger.Log.info("迁移页面处理完成，重新获取页面内容")
                        time.sleep(random.uniform(3, 6))
                
                # 检查是否被删除
                try:
                    deleted_element = driver.find_element(By.CLASS_NAME, 'weui-msg__title.warn')
                    if '该内容已被发布者删除' in deleted_element.text:
                        return {'content': '该内容已被发布者删除'}
                except:
                    pass
                
                # 获取标题
                try:
                    title_element = driver.find_element(By.CSS_SELECTOR, 'meta[property="og:title"]')
                    title = title_element.get_attribute('content')
                    # 清理标题
                    title = self.clean_text(title)
                except:
                    title = ''
                
                # 获取发布时间
                publish_time = ''
                try:
                    scripts = driver.find_elements(By.TAG_NAME, 'script')
                    for script in scripts:
                        script_text = script.get_attribute('innerHTML')
                        if 'var createTime' in script_text:
                            pattern = re.compile(r'.*(var createTime\s*=\s*\'(.*)\'\s*);.*', re.MULTILINE | re.DOTALL)
                            match = pattern.search(script_text)
                            if match:
                                publish_time = CommentUtils.Utils.date_format(match.group(2))
                                break
                except:
                    pass
                
                # 获取正文内容
                try:
                    content_element = driver.find_element(By.ID, 'js_content')
                    content = content_element.text
                    # 清理内容
                    content = self.clean_text(content)
                except:
                    content = ''
                
                # 获取图片
                imgs = ''
                try:
                    images = driver.find_elements(By.CSS_SELECTOR, '#js_content img')
                    img_urls = []
                    for img in images:
                        src = img.get_attribute('data-src') or img.get_attribute('src')
                        if src:
                            img_urls.append(src)
                    imgs = ','.join(img_urls)
                except:
                    pass
                
                return {
                    'title': title,
                    'publishTime': publish_time,
                    'content': content,
                    'imgs': imgs
                }
                
            finally:
                driver.quit()
                
        except ImportError:
            Logger.Log.error("Selenium未安装，无法使用备用方案")
            return None
        except Exception as e:
            Logger.Log.error(f"Selenium获取数据失败: {str(e)}")
            return None
    
    def handle_verification_page(self, driver):
        """处理验证页面，持续点击验证按钮直到成功跳转到文章页面"""
        from selenium.webdriver.common.by import By
        try:
            # 检查是否在验证页面
            current_url = driver.current_url
            page_source = driver.page_source.lower()
            
            Logger.Log.info(f"当前URL: {current_url}")
            Logger.Log.info(f"页面标题: {driver.title}")
            
            # 检查各种验证页面的标识
            verification_indicators = [
                'verify', 'captcha', '验证', '安全验证', '人机验证',
                '请完成验证', '点击验证', '滑动验证', '点选验证'
            ]
            
            is_verification_page = False
            found_indicators = []
            for indicator in verification_indicators:
                if indicator in page_source:
                    is_verification_page = True
                    found_indicators.append(indicator)
            
            if found_indicators:
                Logger.Log.info(f"检测到验证页面，标识: {', '.join(found_indicators)}")
            
            # 检查是否有weui-msg元素（用户提供的验证页面标识）
            try:
                weui_msg_element = driver.find_element(By.XPATH, '//div[@class="weui-msg"]')
                if weui_msg_element:
                    is_verification_page = True
                    Logger.Log.info("检测到weui-msg验证页面元素")
                    Logger.Log.info(f"weui-msg元素文本: {weui_msg_element.text}")
            except Exception as e:
                Logger.Log.info(f"未找到weui-msg元素: {str(e)}")
            
            # 检查页面中的所有按钮元素
            try:
                all_buttons = driver.find_elements(By.TAG_NAME, 'button')
                all_links = driver.find_elements(By.TAG_NAME, 'a')
                Logger.Log.info(f"页面中找到 {len(all_buttons)} 个按钮元素")
                Logger.Log.info(f"页面中找到 {len(all_links)} 个链接元素")
                
                # 显示所有按钮的文本
                for i, button in enumerate(all_buttons[:5]):  # 只显示前5个
                    try:
                        button_text = button.text.strip()
                        if button_text:
                            Logger.Log.info(f"按钮{i+1}: '{button_text}' (可见: {button.is_displayed()}, 可点击: {button.is_enabled()})")
                    except:
                        pass
                
                # 显示所有链接的文本
                for i, link in enumerate(all_links[:5]):  # 只显示前5个
                    try:
                        link_text = link.text.strip()
                        if link_text:
                            Logger.Log.info(f"链接{i+1}: '{link_text}' (可见: {link.is_displayed()}, 可点击: {link.is_enabled()})")
                    except:
                        pass
            except Exception as e:
                Logger.Log.warning(f"获取页面元素信息失败: {str(e)}")
            
            if not is_verification_page:
                Logger.Log.info("当前页面不是验证页面")
                return False
            
            Logger.Log.info("开始处理验证页面...")
            
            # 等待页面完全加载
            time.sleep(random.uniform(2, 4))
            
            # 持续验证直到成功跳转
            max_verification_attempts = 10  # 最多尝试10次验证
            verification_attempts = 0
            
            while verification_attempts < max_verification_attempts:
                verification_attempts += 1
                Logger.Log.info(f"第{verification_attempts}次验证尝试...")
                
                # 检查是否已经跳转到文章页面
                if self.is_article_page(driver):
                    Logger.Log.info("已成功跳转到文章页面")
                    return True
                
                # 尝试点击验证按钮
                verification_clicked = False
                
                try:
                    # 查找验证按钮 - 使用用户提供的XPath
                    verify_button = driver.find_element(By.XPATH, '//a[@class="weui-btn weui-btn_primary"]')
                    
                    if verify_button and verify_button.is_displayed() and verify_button.is_enabled():
                        Logger.Log.info(f"找到验证按钮: {verify_button.text}")
                        
                        # 滚动到元素位置
                        driver.execute_script("arguments[0].scrollIntoView(true);", verify_button)
                        time.sleep(random.uniform(0.5, 1.5))
                        
                        # 尝试点击
                        try:
                            # 使用JavaScript点击（因为按钮没有href属性）
                            driver.execute_script("arguments[0].click();", verify_button)
                            Logger.Log.info("JavaScript点击验证按钮成功")
                            verification_clicked = True
                            
                            # 智能等待验证完成和页面跳转
                            Logger.Log.info("等待验证完成...")
                             
                             # 使用轮询方式检查验证状态，最多等待30秒
                            max_wait_time = 30
                            check_interval = 2
                            waited_time = 0
                             
                            while waited_time < max_wait_time:
                                 time.sleep(check_interval)
                                 waited_time += check_interval
                                 
                                 Logger.Log.info(f"=== 验证状态检查 (第{waited_time//check_interval}次检查，已等待{waited_time}秒) ===")
                                 
                                 # 检查当前URL
                                 current_url = driver.current_url
                                 Logger.Log.info(f"当前URL: {current_url}")
                                 
                                 # 检查页面标题
                                 try:
                                     page_title = driver.title
                                     Logger.Log.info(f"页面标题: {page_title}")
                                 except:
                                     Logger.Log.warning("无法获取页面标题")
                                 
                                 # 检查是否已经跳转到文章页面
                                 if self.is_article_page(driver):
                                     Logger.Log.info(f"✅ 验证完成，已跳转到文章页面 (等待了{waited_time}秒)")
                                     return True
                                 
                                 # 检查URL是否发生变化
                                 if 'verify' not in current_url.lower() and 'captcha' not in current_url.lower():
                                     Logger.Log.info(f"🔄 URL已跳转，验证可能完成 (等待了{waited_time}秒)")
                                     time.sleep(random.uniform(3, 5))  # 再等待一下让页面完全加载
                                     if self.is_article_page(driver):
                                         Logger.Log.info("✅ 验证完成，已跳转到文章页面")
                                         return True
                                 
                                 # 检查是否还在验证页面
                                 new_page_source = driver.page_source.lower()
                                 still_verifying = any(indicator in new_page_source for indicator in verification_indicators)
                                 
                                 # 检查是否还有weui-msg元素
                                 try:
                                     weui_element = driver.find_element(By.XPATH, '//div[@class="weui-msg"]')
                                     still_verifying = True
                                     Logger.Log.info(f"🔍 仍存在weui-msg元素: {weui_element.text[:50]}...")
                                     
                                     # 检查是否有新的验证元素出现（如滑块、拼图等）
                                     try:
                                         # 检查是否有滑块验证
                                         slider_elements = driver.find_elements(By.XPATH, '//div[contains(@class, "slider") or contains(@class, "slide")]')
                                         if slider_elements:
                                             Logger.Log.info(f"🔍 检测到滑块验证元素: {len(slider_elements)}个")
                                         
                                         # 检查是否有拼图验证
                                         puzzle_elements = driver.find_elements(By.XPATH, '//div[contains(@class, "puzzle") or contains(@class, "jigsaw")]')
                                         if puzzle_elements:
                                             Logger.Log.info(f"🔍 检测到拼图验证元素: {len(puzzle_elements)}个")
                                         
                                         # 检查是否有点选验证
                                         click_elements = driver.find_elements(By.XPATH, '//div[contains(@class, "click") or contains(@class, "select")]')
                                         if click_elements:
                                             Logger.Log.info(f"🔍 检测到点选验证元素: {len(click_elements)}个")
                                             
                                     except Exception as e:
                                         Logger.Log.info(f"🔍 检查验证元素时出错: {str(e)}")
                                     
                                 except:
                                     Logger.Log.info("🔍 未找到weui-msg元素")
                                 
                                 # 检查验证按钮状态
                                 try:
                                     verify_button = driver.find_element(By.XPATH, '//a[@class="weui-btn weui-btn_primary"]')
                                     Logger.Log.info(f"🔍 验证按钮状态: 文本='{verify_button.text}', 可见={verify_button.is_displayed()}, 可点击={verify_button.is_enabled()}")
                                 except:
                                     Logger.Log.info("🔍 未找到主要验证按钮")
                                 
                                 if not still_verifying:
                                     Logger.Log.info(f"✅ 验证完成，页面已跳转 (等待了{waited_time}秒)")
                                     return True
                                 
                                 Logger.Log.info(f"⏳ 验证进行中... (已等待{waited_time}秒)")
                                 Logger.Log.info("=" * 50)
                             
                            Logger.Log.warning(f"验证超时，已等待{max_wait_time}秒")
                            Logger.Log.warning("⚠️ 验证可能需要人工干预，建议手动完成验证后重试")
                            
                            # 检查是否还在验证页面
                            new_page_source = driver.page_source.lower()
                            still_verifying = any(indicator in new_page_source for indicator in verification_indicators)
                            
                            # 检查是否还有weui-msg元素
                            try:
                                driver.find_element(By.XPATH, '//div[@class="weui-msg"]')
                                still_verifying = True
                            except:
                                pass
                            
                            if not still_verifying:
                                Logger.Log.info("验证完成，页面已跳转")
                                return True
                            else:
                                Logger.Log.info("验证可能未完成，继续尝试")
                                time.sleep(random.uniform(3, 6))  # 减少等待时间，因为已经有了智能轮询
                                
                        except Exception as click_error:
                            Logger.Log.warning(f"点击验证按钮失败: {str(click_error)}")
                            
                except Exception as e:
                    Logger.Log.warning(f"未找到指定的验证按钮: {str(e)}")
                    
                    # 备用方案：尝试其他常见的验证按钮
                    verification_selectors = [
                        "button[class*='verify']",
                        "button[class*='captcha']", 
                        "button[class*='验证']",
                        "button[class*='安全']",
                        "button[class*='点击']",
                        "button[class*='滑动']",
                        "button[class*='点选']",
                        "div[class*='verify']",
                        "div[class*='captcha']",
                        "div[class*='验证']",
                        "div[class*='安全']",
                        "div[class*='点击']",
                        "div[class*='滑动']",
                        "div[class*='点选']",
                        "span[class*='verify']",
                        "span[class*='captcha']",
                        "span[class*='验证']",
                        "span[class*='安全']",
                        "span[class*='点击']",
                        "span[class*='滑动']",
                        "span[class*='点选']",
                        # 微信特定的选择器
                        "[data-role='verify']",
                        "[data-type='captcha']",
                        "[id*='verify']",
                        "[id*='captcha']",
                        "[class*='weui-btn']",
                        "[class*='btn']",
                        # 最通用的按钮选择器
                        "button",
                        "input[type='button']",
                        "input[type='submit']"
                    ]
                    
                    for selector in verification_selectors:
                        try:
                            # 查找验证按钮
                            elements = driver.find_elements(By.CSS_SELECTOR, selector)
                            
                            for element in elements:
                                try:
                                    # 检查元素是否可见和可点击
                                    if element.is_displayed() and element.is_enabled():
                                        element_text = element.text.lower()
                                        
                                        # 检查按钮文本是否包含验证相关词汇
                                        verification_keywords = ['验证', '点击', '滑动', '点选', '安全', 'verify', 'captcha', '点击验证', '滑动验证']
                                        is_verification_button = any(keyword in element_text for keyword in verification_keywords)
                                        
                                        # 或者检查元素的class/id是否包含验证相关词汇
                                        element_attrs = element.get_attribute('outerHTML').lower()
                                        has_verification_attrs = any(keyword in element_attrs for keyword in verification_keywords)
                                        
                                        if is_verification_button or has_verification_attrs:
                                            Logger.Log.info(f"找到备用验证按钮: {element.text} (选择器: {selector})")
                                            
                                            # 滚动到元素位置
                                            driver.execute_script("arguments[0].scrollIntoView(true);", element)
                                            time.sleep(random.uniform(0.5, 1.5))
                                            
                                            # 尝试点击
                                            try:
                                                # 使用JavaScript点击
                                                driver.execute_script("arguments[0].click();", element)
                                                Logger.Log.info("备用验证按钮JavaScript点击成功")
                                                verification_clicked = True
                                                
                                                # 等待验证完成
                                                time.sleep(random.uniform(3, 6))
                                                
                                                # 检查是否已经跳转到文章页面
                                                if self.is_article_page(driver):
                                                    Logger.Log.info("备用验证完成，已跳转到文章页面")
                                                    return True
                                                
                                                # 检查是否还在验证页面
                                                new_page_source = driver.page_source.lower()
                                                still_verifying = any(indicator in new_page_source for indicator in verification_indicators)
                                                
                                                if not still_verifying:
                                                    Logger.Log.info("备用验证完成，页面已跳转")
                                                    return True
                                                else:
                                                    Logger.Log.info("备用验证可能未完成，继续尝试")
                                                    
                                            except Exception as click_error:
                                                Logger.Log.warning(f"点击备用验证按钮失败: {str(click_error)}")
                                                continue
                                            
                                            break
                                            
                                except Exception as element_error:
                                    continue
                            
                            if verification_clicked:
                                break
                                
                        except Exception as selector_error:
                            continue
                
                # 如果这次尝试没有点击成功，等待后继续
                if not verification_clicked:
                    Logger.Log.warning(f"第{verification_attempts}次验证尝试失败，等待后重试...")
                    time.sleep(random.uniform(3, 6))
            
            # 如果所有尝试都失败，记录日志
            Logger.Log.warning(f"经过{max_verification_attempts}次验证尝试后仍未能成功跳转")
            return False
            
        except Exception as e:
            Logger.Log.error(f"处理验证页面时出错: {str(e)}")
            return False
    
    def is_article_page(self, driver):
        """检查是否已经跳转到文章页面"""
        from selenium.webdriver.common.by import By
        try:
            # 检查多种可能的内容元素
            content_selectors = [
                '//div[@id="js_content"]',
                '//div[contains(@class, "rich_media_content")]',
                '//div[contains(@class, "content")]',
                '//article',
                '//div[contains(@class, "article")]',
                '//div[contains(@class, "post")]'
            ]
            
            for selector in content_selectors:
                try:
                    element = driver.find_element(By.XPATH, selector)
                    if element and element.text.strip():
                        Logger.Log.info(f"检测到文章内容元素: {selector}")
                        return True
                except:
                    continue
            
            # 检查是否有文章标题
            try:
                title_element = driver.find_element(By.CSS_SELECTOR, 'meta[property="og:title"]')
                if title_element and title_element.get_attribute('content'):
                    Logger.Log.info("检测到文章标题元素")
                    return True
            except:
                pass
            
            # 检查URL是否包含文章标识
            current_url = driver.current_url
            if '/s?' in current_url and '__biz=' in current_url:
                Logger.Log.info("检测到文章URL")
                return True
            
            # 检查页面是否包含验证相关元素（如果还在验证页面，说明没有跳转成功）
            try:
                driver.find_element(By.XPATH, '//div[@class="weui-msg"]')
                return False  # 还在验证页面
            except:
                pass
            
            # 检查页面标题（但需要排除验证页面的标题）
            try:
                title = driver.title.lower()
                # 排除验证相关的标题
                verification_titles = ['验证', 'captcha', 'verify', '安全验证', '人机验证']
                if any(keyword in title for keyword in verification_titles):
                    return False  # 验证页面的标题
                
                if title and len(title.strip()) > 5:
                    Logger.Log.info("检测到页面标题")
                    return True
            except:
                pass
            
            return False
            
        except Exception as e:
            Logger.Log.warning(f"检查文章页面时出错: {str(e)}")
            return False
    
    def get_data_with_requests(self, url):
        """使用requests直接访问微信公众号"""
        try:
            # 添加微信特定头部
            self.add_wechat_headers(url)
            
            # 模拟人类行为
            self.simulate_human_behavior()
            
            # 获取代理并设置
            proxy = self.get_next_proxy()
            self.setup_proxy_for_requests(proxy)
            
            # 发送请求
            response = self.session.get(
                url, 
                timeout=30, 
                allow_redirects=True
            )
            response.raise_for_status()
            
            # 处理编码
            try:
                response.encoding = response.apparent_encoding
            except:
                response.encoding = 'utf-8'
            
            # 检查是否被重定向到验证页面
            if 'verify' in response.url.lower() or 'captcha' in response.url.lower():
                Logger.Log.warning(f"被重定向到验证页面: {response.url}")
                return None
            
            # 检查页面内容是否包含验证相关文本
            response_text = response.text.lower()
            verification_keywords = ['验证', '安全验证', '人机验证', '请完成验证', '点击验证', '滑动验证', '点选验证', 'verify', 'captcha']
            if any(keyword in response_text for keyword in verification_keywords):
                Logger.Log.warning("页面包含验证内容，需要Selenium处理")
                return None
            
            soup = BeautifulSoup(response.text, "html.parser")
            
            # 检查是否被删除
            if soup.find(attrs={'class': 'weui-msg__title warn'}) is not None and \
                    '该内容已被发布者删除' in soup.find(attrs={'class': 'weui-msg__title warn'}).text:
                return {'content': '该内容已被发布者删除'}
            
            # 获取标题
            title = ''
            try:
                title_element = soup.find(attrs={'property': 'og:title'})
                if title_element:
                    title = title_element.attrs['content']
                else:
                    # 备用方案：从页面标题获取
                    title_element = soup.find('title')
                    if title_element:
                        title = title_element.text.strip()
                
                # 清理标题
                title = self.clean_text(title)
            except Exception as e:
                Logger.Log.error(f"获取标题失败: {str(e)}")
                title = ''
            
            # 获取发布时间
            publish_time = ''
            try:
                # 多种时间格式的正则表达式
                time_patterns = [
                    r'var createTime\s*=\s*[\'"]([^\'"]+)[\'"]',
                    r'publish_time[\'"]?\s*:\s*[\'"]([^\'"]+)[\'"]',
                    r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})',
                    r'(\d{4}年\d{1,2}月\d{1,2}日)'
                ]
                
                scripts = soup.findAll('script')
                for script in scripts:
                    script_text = script.text
                    for pattern in time_patterns:
                        match = re.search(pattern, script_text)
                        if match:
                            try:
                                publish_time = CommentUtils.Utils.date_format(match.group(1))
                                break
                            except:
                                continue
                    if publish_time:
                        break
            except Exception as e:
                Logger.Log.error(f"获取发布时间失败: {str(e)}")
                publish_time = ''
            
            # 获取正文内容
            content = ''
            try:
                content_element = soup.find('div', {'id': 'js_content'})
                if content_element:
                    # 移除脚本和样式标签
                    for script in content_element(["script", "style"]):
                        script.decompose()
                    content = content_element.get_text(separator='\n', strip=True)
                    # 清理内容
                    content = self.clean_text(content)
            except Exception as e:
                Logger.Log.error(f"获取正文内容失败: {str(e)}")
                content = ''
            
            # 获取图片
            imgs = ''
            try:
                if content_element:
                    images = content_element.findAll('img')
                    img_urls = []
                    for img in images:
                        src = img.attrs.get('data-src') or img.attrs.get('src')
                        if src and not src.startswith('data:'):
                            img_urls.append(src)
                    imgs = ','.join(img_urls)
            except Exception as e:
                Logger.Log.error(f"获取图片失败: {str(e)}")
                imgs = ''
            
            data = {
                'title': title,
                'publishTime': publish_time,
                'content': content,
                'imgs': imgs
            }
            
            # 验证数据
            if not self.validate_data(data):
                Logger.Log.warning("数据验证失败，可能获取不完整")
            
            return data
            
        except Exception as e:
            error_type = self.handle_network_errors(e)
            Logger.Log.error(f"requests获取方法失败: {str(e)} (错误类型: {error_type})")
            return None
    
    def get_data(self, msg):
        """主要的数据获取方法 - 优先使用requests，失败时使用Selenium"""
        try:
            url = msg['url']
            max_retries = 2  # 减少重试次数，避免多次打开网页
            
            for attempt in range(max_retries):
                try:
                    Logger.Log.info(f"尝试第{attempt + 1}次获取: {url}")
                    
                    # 优先使用requests直接访问
                    Logger.Log.info("尝试使用requests直接访问微信公众号")
                    data = self.get_data_with_requests(url)
                    if data:
                        Logger.Log.info("requests访问成功")
                        break
                    
                    # 如果requests失败，使用Selenium
                    Logger.Log.info("requests访问失败，尝试使用Selenium")
                    data = self.get_data_with_selenium_fallback(url)
                    if data:
                        Logger.Log.info("Selenium访问成功")
                        break
                    
                    # 如果失败，等待后重试
                    if attempt < max_retries - 1:
                        wait_time = random.uniform(10, 20)
                        Logger.Log.info(f"等待{wait_time:.1f}秒后重试...")
                        time.sleep(wait_time)
                    
                except Exception as e:
                    Logger.Log.error(f"第{attempt + 1}次尝试失败: {str(e)}")
                    if attempt < max_retries - 1:
                        time.sleep(random.uniform(30, 60))  # 增加等待时间
                    else:
                        raise e
            
            if not data:
                raise Exception("所有获取方法都失败")
            
            # 构建返回数据
            result_data = {
                'taskSid': msg['taskSid'],
                'itemSid': msg['itemSid'],
                'dataType': 'lookback_wechat_advanced',
                'publishTime': data.get('publishTime', ''),
                'title': data.get('title', ''),
                'content': data.get('content', ''),
                'imgs': data.get('imgs', '')
            }
            
            # 推送结果
            # 注释掉消息队列推送，批量爬虫不需要此功能
            # RabbitMQUtils.Helper.push_wechat_lookback_result(result_data)
            Logger.Log.info("result:" + json.dumps(result_data, ensure_ascii=False))
            
            # 随机延迟（减少等待时间）
            time.sleep(random.uniform(3, 8))
            
            # 返回获取到的数据
            return result_data
            
        except Exception as e:
            Logger.Log.error(f"获取数据失败: {str(e)}")
            if 'Invalid URL' in str(e) or 'Max retries exceeded with url' in str(e):
                raise Exception('Invalid URL')
            else:
                raise e
        
        return None


    def get_data_optimized(self, msg):
        """简化的数据获取方法 - 精确识别页面类型并处理"""
        try:
            url = msg.get('url', '')
            if not url:
                Logger.Log.error("URL为空")
                return None
            
            Logger.Log.info(f"开始处理URL: {url}")
            
            # 初始化浏览器
            driver = self.init_browser()
            if not driver:
                Logger.Log.error("浏览器初始化失败")
                return None
            
            try:
                # 访问URL
                driver.get(url)
                time.sleep(3)
                
                # 主循环：检测页面类型并处理
                max_attempts = 3
                for attempt in range(max_attempts):
                    Logger.Log.info(f"第 {attempt + 1} 次检测页面类型")
                    
                    # 1. 首先检查是否为验证页面
                    if self.is_verification_page_simple(driver):
                        Logger.Log.info("检测到验证页面，处理验证...")
                        if self.handle_verification_simple(driver):
                            Logger.Log.info("验证处理成功，继续检测...")
                            time.sleep(2)
                            continue
                        else:
                            Logger.Log.warning("验证处理失败")
                            return None
                    
                    # 2. 检查是否为迁移页面
                    elif self.is_migration_page_simple(driver):
                        Logger.Log.info("检测到迁移页面，处理迁移...")
                        if self.handle_migration_simple(driver):
                            Logger.Log.info("迁移处理成功，继续检测...")
                            time.sleep(2)
                            continue
                        else:
                            Logger.Log.warning("迁移处理失败")
                            return None
                    
                    # 3. 检查是否为文章页面
                    elif self.is_article_page_simple(driver):
                        Logger.Log.info("检测到文章页面，立即提取数据...")
                        result = self.extract_article_data_simple(driver)
                        if result:
                            Logger.Log.info("数据提取成功")
                            return result
                        else:
                            Logger.Log.warning("数据提取失败")
                            return None
                    
                    # 4. 未知页面类型
                    else:
                        Logger.Log.warning("未知页面类型，尝试等待...")
                        time.sleep(3)
                        continue
                
                Logger.Log.warning(f"经过 {max_attempts} 次尝试仍未成功")
                return None
                
            finally:
                try:
                    driver.quit()
                except:
                    pass
                    
        except Exception as e:
            Logger.Log.error(f"数据获取过程中出现异常: {str(e)}")
            return None

    def init_browser(self):
        """初始化浏览器（Selenium）"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 使用当前目录的ChromeDriver
            import os
            # chromedriver_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'chromedriver.exe')
            chromedriver_path = '/Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/chromedriver-mac-arm64/chromedriver'

            chrome_options.binary_location = r"/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
            from selenium.webdriver.chrome.service import Service
            service = Service(chromedriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
            Logger.Log.info("使用Chrome浏览器（本地ChromeDriver）")
            
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except ImportError:
            Logger.Log.error("Selenium未安装，无法使用优化版Selenium方案")
            return None
        except Exception as e:
            Logger.Log.error(f"浏览器初始化失败: {str(e)}")
            return None

    def is_verification_page_simple(self, driver):
        """精确检查是否为验证页面 - 借鉴原始代码逻辑"""
        from selenium.webdriver.common.by import By
        
        try:
            current_url = driver.current_url
            page_source = driver.page_source.lower()
            
            Logger.Log.info(f"当前URL: {current_url}")
            Logger.Log.info(f"页面标题: {driver.title}")
            
            # 首先检查URL是否包含验证页面标识
            if 'wappoc_appmsgcaptcha' in current_url:
                Logger.Log.info("检测到验证页面URL标识")
                return True
            
            # 检查各种验证页面的标识 - 借鉴原始代码的关键词
            verification_indicators = [
                'verify', 'captcha', '验证', '安全验证', '人机验证',
                '请完成验证', '点击验证', '滑动验证', '点选验证', '环境异常，需要验证'
            ]
            
            is_verification_page = False
            found_indicators = []
            for indicator in verification_indicators:
                if indicator in page_source:
                    is_verification_page = True
                    found_indicators.append(indicator)
            
            if found_indicators:
                Logger.Log.info(f"检测到验证页面，标识: {', '.join(found_indicators)}")
            
            # 检查是否有weui-msg元素（借鉴原始代码逻辑）
            try:
                weui_msg_element = driver.find_element(By.XPATH, '//div[@class="weui-msg"]')
                if weui_msg_element:
                    # 借鉴原始代码：检查weui-msg元素内是否包含验证相关文本
                    weui_text = weui_msg_element.text.lower()
                    verification_texts = ['环境异常，需要验证', '请完成验证', '点击验证', '滑动验证', '点选验证', '安全验证', '人机验证']
                    if any(text in weui_text for text in verification_texts):
                        is_verification_page = True
                        Logger.Log.info("检测到weui-msg验证页面元素")
                        Logger.Log.info(f"weui-msg元素文本: {weui_msg_element.text}")
                    else:
                        Logger.Log.info("weui-msg元素存在但不包含验证文本，可能是文章页面")
                        return False
            except Exception as e:
                Logger.Log.info(f"未找到weui-msg元素: {str(e)}")
            
            # 借鉴原始代码：检查页面中的所有按钮元素，帮助调试
            try:
                all_buttons = driver.find_elements(By.TAG_NAME, 'button')
                all_links = driver.find_elements(By.TAG_NAME, 'a')
                Logger.Log.info(f"页面中找到 {len(all_buttons)} 个按钮元素")
                Logger.Log.info(f"页面中找到 {len(all_links)} 个链接元素")
                
                # 显示所有按钮的文本（借鉴原始代码的调试方式）
                for i, button in enumerate(all_buttons[:5]):  # 只显示前5个
                    try:
                        button_text = button.text.strip()
                        if button_text:
                            Logger.Log.info(f"按钮{i+1}: '{button_text}' (可见: {button.is_displayed()}, 可点击: {button.is_enabled()})")
                    except:
                        pass
                
                # 显示所有链接的文本
                for i, link in enumerate(all_links[:5]):  # 只显示前5个
                    try:
                        link_text = link.text.strip()
                        if link_text:
                            Logger.Log.info(f"链接{i+1}: '{link_text}' (可见: {link.is_displayed()}, 可点击: {link.is_enabled()})")
                    except:
                        pass
            except Exception as e:
                Logger.Log.warning(f"获取页面元素信息失败: {str(e)}")
            
            # 如果检测到验证页面，还要检查是否同时存在文章内容
            if is_verification_page:
                try:
                    # 检查是否同时存在文章内容元素
                    content_element = driver.find_element(By.ID, 'js_content')
                    if content_element and content_element.text.strip():
                        Logger.Log.info("检测到文章内容元素，页面可能是文章页面而非验证页面")
                        return False
                except:
                    pass
            
            return is_verification_page
        except Exception as e:
            Logger.Log.error(f"检查验证页面时出错: {str(e)}")
            return False

    def handle_verification_simple(self, driver):
        """精确处理验证页面 - 借鉴原始代码逻辑"""
        from selenium.webdriver.common.by import By
        import time
        import random
        
        try:
            Logger.Log.info("开始处理验证页面...")
            
            # 等待页面完全加载
            time.sleep(random.uniform(2, 4))
            
            # 持续验证直到成功跳转
            max_verification_attempts = 10  # 借鉴原始代码：最多尝试10次验证
            verification_attempts = 0
            
            while verification_attempts < max_verification_attempts:
                verification_attempts += 1
                Logger.Log.info(f"第{verification_attempts}次验证尝试...")
                
                # 检查是否已经跳转到文章页面
                if self.is_article_page_simple(driver):
                    Logger.Log.info("已成功跳转到文章页面")
                    return True
                
                # 尝试点击验证按钮
                verification_clicked = False
                
                try:
                    # 查找验证按钮 - 使用用户提供的XPath（借鉴原始代码）
                    verify_button = driver.find_element(By.XPATH, '//a[@class="weui-btn weui-btn_primary"]')
                    
                    if verify_button and verify_button.is_displayed() and verify_button.is_enabled():
                        Logger.Log.info(f"找到验证按钮: {verify_button.text}")
                        
                        # 滚动到元素位置
                        driver.execute_script("arguments[0].scrollIntoView(true);", verify_button)
                        time.sleep(random.uniform(0.5, 1.5))
                        
                        # 尝试点击
                        try:
                            # 使用JavaScript点击（因为按钮没有href属性）
                            driver.execute_script("arguments[0].click();", verify_button)
                            Logger.Log.info("JavaScript点击验证按钮成功")
                            verification_clicked = True
                            
                            # 借鉴原始代码：智能等待验证完成和页面跳转
                            Logger.Log.info("等待验证完成...")
                            
                            # 使用轮询方式检查验证状态，最多等待30秒（借鉴原始代码）
                            max_wait_time = 30
                            check_interval = 2
                            waited_time = 0
                            
                            while waited_time < max_wait_time:
                                time.sleep(check_interval)
                                waited_time += check_interval
                                
                                Logger.Log.info(f"=== 验证状态检查 (第{waited_time//check_interval}次检查，已等待{waited_time}秒) ===")
                                
                                # 检查当前URL
                                current_url = driver.current_url
                                Logger.Log.info(f"当前URL: {current_url}")
                                
                                # 检查页面标题
                                try:
                                    page_title = driver.title
                                    Logger.Log.info(f"页面标题: {page_title}")
                                except:
                                    Logger.Log.warning("无法获取页面标题")
                                
                                # 检查是否已经跳转到文章页面
                                if self.is_article_page_simple(driver):
                                    Logger.Log.info(f"✅ 验证完成，已跳转到文章页面 (等待了{waited_time}秒)")
                                    return True
                                
                                # 检查URL是否发生变化
                                if 'verify' not in current_url.lower() and 'captcha' not in current_url.lower():
                                    Logger.Log.info(f"🔄 URL已跳转，验证可能完成 (等待了{waited_time}秒)")
                                    time.sleep(random.uniform(3, 5))  # 再等待一下让页面完全加载
                                    if self.is_article_page_simple(driver):
                                        Logger.Log.info("✅ 验证完成，已跳转到文章页面")
                                        return True
                                
                                # 检查是否还在验证页面
                                new_page_source = driver.page_source.lower()
                                verification_indicators = ['verify', 'captcha', '验证', '安全验证', '人机验证', '请完成验证', '点击验证', '滑动验证', '点选验证']
                                still_verifying = any(indicator in new_page_source for indicator in verification_indicators)
                                
                                # 检查是否还有weui-msg元素
                                try:
                                    weui_element = driver.find_element(By.XPATH, '//div[@class="weui-msg"]')
                                    still_verifying = True
                                    Logger.Log.info(f"🔍 仍存在weui-msg元素: {weui_element.text[:50]}...")
                                    
                                    # 检查是否有新的验证元素出现（如滑块、拼图等）
                                    try:
                                        # 检查是否有滑块验证
                                        slider_elements = driver.find_elements(By.XPATH, '//div[contains(@class, "slider") or contains(@class, "slide")]')
                                        if slider_elements:
                                            Logger.Log.info(f"🔍 检测到滑块验证元素: {len(slider_elements)}个")
                                        
                                        # 检查是否有拼图验证
                                        puzzle_elements = driver.find_elements(By.XPATH, '//div[contains(@class, "puzzle") or contains(@class, "jigsaw")]')
                                        if puzzle_elements:
                                            Logger.Log.info(f"🔍 检测到拼图验证元素: {len(puzzle_elements)}个")
                                        
                                        # 检查是否有点选验证
                                        click_elements = driver.find_elements(By.XPATH, '//div[contains(@class, "click") or contains(@class, "select")]')
                                        if click_elements:
                                            Logger.Log.info(f"🔍 检测到点选验证元素: {len(click_elements)}个")
                                            
                                    except Exception as e:
                                        Logger.Log.info(f"🔍 检查验证元素时出错: {str(e)}")
                                    
                                except:
                                    Logger.Log.info("🔍 未找到weui-msg元素")
                                
                                # 检查验证按钮状态
                                try:
                                    verify_button = driver.find_element(By.XPATH, '//a[@class="weui-btn weui-btn_primary"]')
                                    Logger.Log.info(f"🔍 验证按钮状态: 文本='{verify_button.text}', 可见={verify_button.is_displayed()}, 可点击={verify_button.is_enabled()}")
                                except:
                                    Logger.Log.info("🔍 未找到主要验证按钮")
                                
                                if not still_verifying:
                                    Logger.Log.info(f"✅ 验证完成，页面已跳转 (等待了{waited_time}秒)")
                                    return True
                                
                                Logger.Log.info(f"⏳ 验证进行中... (已等待{waited_time}秒)")
                                Logger.Log.info("=" * 50)
                            
                            Logger.Log.warning(f"验证超时，已等待{max_wait_time}秒")
                            Logger.Log.warning("⚠️ 验证可能需要人工干预，建议手动完成验证后重试")
                            
                            # 检查是否还在验证页面
                            new_page_source = driver.page_source.lower()
                            still_verifying = any(indicator in new_page_source for indicator in verification_indicators)
                            
                            # 检查是否还有weui-msg元素
                            try:
                                driver.find_element(By.XPATH, '//div[@class="weui-msg"]')
                                still_verifying = True
                            except:
                                pass
                            
                            if not still_verifying:
                                Logger.Log.info("验证完成，页面已跳转")
                                return True
                            else:
                                Logger.Log.info("验证可能未完成，继续尝试")
                                time.sleep(random.uniform(3, 6))  # 减少等待时间，因为已经有了智能轮询
                                
                        except Exception as click_error:
                            Logger.Log.warning(f"点击验证按钮失败: {str(click_error)}")
                            
                except Exception as e:
                    Logger.Log.warning(f"未找到指定的验证按钮: {str(e)}")
                    
                    # 借鉴原始代码：备用方案：尝试其他常见的验证按钮
                    verification_selectors = [
                        "button[class*='verify']",
                        "button[class*='captcha']", 
                        "button[class*='验证']",
                        "button[class*='安全']",
                        "button[class*='点击']",
                        "button[class*='滑动']",
                        "button[class*='点选']",
                        "div[class*='verify']",
                        "div[class*='captcha']",
                        "div[class*='验证']",
                        "div[class*='安全']",
                        "div[class*='点击']",
                        "div[class*='滑动']",
                        "div[class*='点选']",
                        "span[class*='verify']",
                        "span[class*='captcha']",
                        "span[class*='验证']",
                        "span[class*='安全']",
                        "span[class*='点击']",
                        "span[class*='滑动']",
                        "span[class*='点选']",
                        # 微信特定的选择器
                        "[data-role='verify']",
                        "[data-type='captcha']",
                        "[id*='verify']",
                        "[id*='captcha']",
                        "[class*='weui-btn']",
                        "[class*='btn']",
                        # 最通用的按钮选择器
                        "button",
                        "input[type='button']",
                        "input[type='submit']"
                    ]
                    
                    for selector in verification_selectors:
                        try:
                            # 查找验证按钮
                            elements = driver.find_elements(By.CSS_SELECTOR, selector)
                            
                            for element in elements:
                                try:
                                    # 检查元素是否可见和可点击
                                    if element.is_displayed() and element.is_enabled():
                                        element_text = element.text.lower()
                                        
                                        # 检查按钮文本是否包含验证相关词汇
                                        verification_keywords = ['验证', '点击', '滑动', '点选', '安全', 'verify', 'captcha', '点击验证', '滑动验证']
                                        is_verification_button = any(keyword in element_text for keyword in verification_keywords)
                                        
                                        # 或者检查元素的class/id是否包含验证相关词汇
                                        element_attrs = element.get_attribute('outerHTML').lower()
                                        has_verification_attrs = any(keyword in element_attrs for keyword in verification_keywords)
                                        
                                        if is_verification_button or has_verification_attrs:
                                            Logger.Log.info(f"找到备用验证按钮: {element.text} (选择器: {selector})")
                                            
                                            # 滚动到元素位置
                                            driver.execute_script("arguments[0].scrollIntoView(true);", element)
                                            time.sleep(random.uniform(0.5, 1.5))
                                            
                                            # 尝试点击
                                            try:
                                                # 使用JavaScript点击
                                                driver.execute_script("arguments[0].click();", element)
                                                Logger.Log.info("备用验证按钮JavaScript点击成功")
                                                verification_clicked = True
                                                
                                                # 等待验证完成
                                                time.sleep(random.uniform(3, 6))
                                                
                                                # 检查是否已经跳转到文章页面
                                                if self.is_article_page_simple(driver):
                                                    Logger.Log.info("备用验证完成，已跳转到文章页面")
                                                    return True
                                                
                                                # 检查是否还在验证页面
                                                new_page_source = driver.page_source.lower()
                                                still_verifying = any(indicator in new_page_source for indicator in verification_indicators)
                                                
                                                if not still_verifying:
                                                    Logger.Log.info("备用验证完成，页面已跳转")
                                                    return True
                                                else:
                                                    Logger.Log.info("备用验证可能未完成，继续尝试")
                                                    
                                            except Exception as click_error:
                                                Logger.Log.warning(f"点击备用验证按钮失败: {str(click_error)}")
                                                continue
                                            
                                            break
                                            
                                except Exception as element_error:
                                    continue
                            
                            if verification_clicked:
                                break
                                
                        except Exception as selector_error:
                            continue
                
                # 如果这次尝试没有点击成功，等待后继续
                if not verification_clicked:
                    Logger.Log.warning(f"第{verification_attempts}次验证尝试失败，等待后重试...")
                    time.sleep(random.uniform(3, 6))
            
            # 如果所有尝试都失败，记录日志
            Logger.Log.warning(f"经过{max_verification_attempts}次验证尝试后仍未能成功跳转")
            return False
            
        except Exception as e:
            Logger.Log.error(f"处理验证页面时出错: {str(e)}")
            return False

    def is_migration_page_simple(self, driver):
        """精确检查是否为迁移页面 - 借鉴原始代码逻辑"""
        from selenium.webdriver.common.by import By
        
        try:
            # 检查迁移页面特有元素
            try:
                migration_title = driver.find_element(By.XPATH, '//div[@class="weui-msg__text-area"]/h2[@class="weui-msg__title"]')
                migration_link = driver.find_element(By.XPATH, '//div[@class="weui-msg__opr-area"]/p/a[1]')
                
                if migration_title and migration_link:
                    Logger.Log.info("检测到迁移页面元素")
                    Logger.Log.info(f"迁移标题: {migration_title.text}")
                    Logger.Log.info(f"迁移链接: {migration_link.get_attribute('href')}")
                    return True
            except Exception as e:
                Logger.Log.info(f"未找到迁移页面元素: {str(e)}")
            
            # 检查页面源码中的迁移关键词
            page_source = driver.page_source
            migration_keywords = ['账号已迁移', '迁移', '该账号已迁移']
            if any(keyword in page_source for keyword in migration_keywords):
                Logger.Log.info("检测到迁移页面关键词")
                return True
            
            return False
        except Exception as e:
            Logger.Log.error(f"检查迁移页面时出错: {str(e)}")
            return False

    def handle_migration_simple(self, driver):
        """精确处理迁移页面 - 借鉴原始代码逻辑"""
        from selenium.webdriver.common.by import By
        import time
        import random
        
        try:
            Logger.Log.info("开始处理迁移页面...")
            
            # 查找迁移链接
            migration_link = driver.find_element(By.XPATH, '//div[@class="weui-msg__opr-area"]/p/a[1]')
            
            if migration_link:
                href = migration_link.get_attribute('href')
                if href:
                    Logger.Log.info(f"找到迁移链接: {href}")
                    
                    # 点击迁移链接
                    try:
                        migration_link.click()
                        Logger.Log.info("点击迁移链接成功")
                    except:
                        # 尝试JavaScript点击
                        driver.execute_script("arguments[0].click();", migration_link)
                        Logger.Log.info("使用JavaScript点击迁移链接")
                    
                    # 等待页面跳转
                    time.sleep(random.uniform(3, 6))
                    Logger.Log.info("迁移处理完成")
                    return True
                else:
                    Logger.Log.warning("迁移链接没有href属性")
                    return False
            else:
                Logger.Log.warning("未找到迁移链接")
                return False
                
        except Exception as e:
            Logger.Log.error(f"迁移处理失败: {str(e)}")
            return False

    def is_article_page_simple(self, driver):
        """精确检查是否为文章页面 - 借鉴原始代码逻辑"""
        from selenium.webdriver.common.by import By
        
        try:
            # 检查是否有文章内容元素
            try:
                content_element = driver.find_element(By.ID, 'js_content')
                if content_element and content_element.text.strip():
                    Logger.Log.info("检测到文章内容元素")
                    Logger.Log.info(f"文章内容长度: {len(content_element.text)}")
                    return True
            except Exception as e:
                Logger.Log.info(f"未找到js_content元素: {str(e)}")
            
            # 检查是否有文章标题
            try:
                title_element = driver.find_element(By.CSS_SELECTOR, 'meta[property="og:title"]')
                if title_element and title_element.get_attribute('content'):
                    Logger.Log.info("检测到文章标题元素")
                    Logger.Log.info(f"文章标题: {title_element.get_attribute('content')}")
                    return True
            except Exception as e:
                Logger.Log.info(f"未找到文章标题元素: {str(e)}")
            
            # 检查URL是否包含文章标识
            current_url = driver.current_url
            if '/s?' in current_url and '__biz=' in current_url:
                Logger.Log.info("检测到文章URL特征")
                return True
            
            # 检查页面是否包含验证相关元素（如果还在验证页面，说明没有跳转成功）
            try:
                weui_element = driver.find_element(By.XPATH, '//div[@class="weui-msg"]')
                weui_text = weui_element.text.lower()
                verification_texts = ['环境异常，需要验证', '请完成验证', '点击验证', '滑动验证', '点选验证', '安全验证', '人机验证']
                if any(text in weui_text for text in verification_texts):
                    Logger.Log.info("检测到验证页面元素，不是文章页面")
                    return False
            except:
                pass
            
            return False
        except Exception as e:
            Logger.Log.error(f"检查文章页面时出错: {str(e)}")
            return False

    def extract_article_data_simple(self, driver):
        """精确提取文章数据 - 借鉴原始代码逻辑"""
        from selenium.webdriver.common.by import By
        
        try:
            Logger.Log.info("开始提取文章数据...")
            
            # 提取标题
            title = ""
            try:
                # 优先从meta标签获取标题
                title_element = driver.find_element(By.CSS_SELECTOR, 'meta[property="og:title"]')
                if title_element:
                    title = title_element.get_attribute('content')
                    Logger.Log.info(f"从meta标签提取到标题: {title[:50]}...")
            except Exception as e:
                Logger.Log.info(f"从meta标签获取标题失败: {str(e)}")
            
            # 如果meta标签没有获取到，尝试从页面标题获取
            if not title:
                try:
                    title_element = driver.find_element(By.TAG_NAME, 'title')
                    if title_element:
                        title = title_element.text.strip()
                        Logger.Log.info(f"从页面标题提取到标题: {title[:50]}...")
                except Exception as e:
                    Logger.Log.info(f"从页面标题获取标题失败: {str(e)}")
            
            # 如果还是没有，尝试从h1标签获取
            if not title:
                try:
                    title_element = driver.find_element(By.XPATH, '//h1[@id="activity-name"]')
                    if title_element:
                        title = title_element.text.strip()
                        Logger.Log.info(f"从h1标签提取到标题: {title[:50]}...")
                except Exception as e:
                    Logger.Log.info(f"从h1标签获取标题失败: {str(e)}")
            
            # 提取内容
            content = ""
            try:
                content_element = driver.find_element(By.ID, 'js_content')
                if content_element:
                    # 移除脚本和样式标签
                    scripts = content_element.find_elements(By.TAG_NAME, 'script')
                    styles = content_element.find_elements(By.TAG_NAME, 'style')
                    
                    for script in scripts:
                        driver.execute_script("arguments[0].remove();", script)
                    for style in styles:
                        driver.execute_script("arguments[0].remove();", style)
                    
                    content = content_element.text.strip()
                    Logger.Log.info(f"提取到内容: {len(content)} 字符")
            except Exception as e:
                Logger.Log.error(f"提取内容失败: {str(e)}")
            
            # 修复编码
            if title:
                title = self.fix_encoding(title)
                title = self.clean_text(title)
            if content:
                content = self.fix_encoding(content)
                content = self.clean_text(content)
            
            if title or content:
                result = {
                    'title': title,
                    'content': content
                }
                Logger.Log.info("数据提取完成")
                return result
            else:
                Logger.Log.warning("未能提取到有效数据")
                return None
                
        except Exception as e:
            Logger.Log.error(f"数据提取失败: {str(e)}")
            return None

    def detect_page_type(self, driver):
        """检测当前页面类型"""
        try:
            page_source = driver.page_source.lower()
            
            # 检查是否包含验证相关文本 - 用户指定的方法
            verification_keywords = ['环境异常，需要验证', '请完成验证', '点击验证', '滑动验证', '点选验证', 'verify', 'captcha']
            if any(keyword in page_source for keyword in verification_keywords):
                Logger.Log.info("检测到验证页面")
                return "verification"
            
            # 检查是否包含公众号迁移相关文本
            migration_keywords = ['账号已迁移', '迁移']
            if any(keyword in page_source for keyword in migration_keywords):
                Logger.Log.info("检测到迁移页面")
                return "migration"
            
            # 最后检查是否包含文章内容
            content_selectors = [
                '//div[@id="js_content"]',
                '//div[contains(@class, "rich_media_content")]',
                '//div[contains(@class, "content")]',
                '//article',
                '//div[contains(@class, "article")]',
                '//div[contains(@class, "post")]'
            ]
            for selector in content_selectors:
                try:
                    element = driver.find_element(By.XPATH, selector)
                    if element and element.text.strip():
                        Logger.Log.info(f"检测到文章内容元素: {selector}")
                        return "article"
                except:
                    continue
            
            # 检查URL是否包含文章特征
            current_url = driver.current_url
            if '/s?' in current_url and '__biz=' in current_url:
                Logger.Log.info("检测到文章URL")
                return "article"
            
            # 检查页面标题是否包含文章标题特征
            try:
                title = driver.title
                if title and len(title.strip()) > 5 and not any(keyword in title.lower() for keyword in ['验证', 'captcha', 'verify']):
                    Logger.Log.info("检测到文章标题")
                    return "article"
            except:
                pass
            
            # 默认返回未知
            return "unknown"
        except Exception as e:
            Logger.Log.error(f"检测页面类型失败: {str(e)}")
            return "unknown"

    def get_data_with_selenium_optimized(self, url):
        """优化版Selenium获取方法 - 只尝试一次验证，失败后跳过"""
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from selenium.common.exceptions import TimeoutException, NoSuchElementException
        
        driver = None
        try:
            # 设置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 使用当前目录的ChromeDriver
            import os
            chromedriver_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'chromedriver.exe')
            chrome_options.binary_location = r"C:\Program Files (x86)\Qoom\Chrome\chrome.exe"
            from selenium.webdriver.chrome.service import Service
            service = Service(chromedriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
            Logger.Log.info("使用Chrome浏览器（本地ChromeDriver）")
            
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            Logger.Log.info("开始访问URL...")
            driver.get(url)
            
            # 等待页面加载
            time.sleep(random.uniform(3, 5))
            
            # 检查是否需要验证
            if self.is_verification_page_optimized(driver):
                Logger.Log.info("检测到验证页面，尝试一次验证...")
                
                # 只尝试一次验证
                if self.handle_verification_once(driver):
                    Logger.Log.info("验证成功，继续获取数据...")
                    time.sleep(random.uniform(8, 15))  # 验证后等待更长时间
                else:
                    Logger.Log.warning("验证失败或超时，跳过此URL")
                    return None
            else:
                Logger.Log.info("无需验证，直接获取数据...")
            
            # 检查是否是公众号迁移页面
            if self.is_migration_page(driver):
                Logger.Log.info("检测到公众号迁移页面，尝试点击迁移链接...")
                
                if self.handle_migration_page(driver):
                    Logger.Log.info("迁移页面处理成功，继续获取数据...")
                    time.sleep(random.uniform(8, 15))  # 迁移后等待更长时间
                else:
                    Logger.Log.warning("迁移页面处理失败，跳过此URL")
                    return None
            
            # 检查是否成功进入文章页面
            if not self.is_article_page(driver):
                Logger.Log.warning("未能进入文章页面，跳过此URL")
                return None
            
            # 等待页面完全加载
            Logger.Log.info("等待页面完全加载...")
            time.sleep(random.uniform(3, 5))
            
            # 获取文章数据
            return self.extract_article_data_optimized(driver)
            
        except Exception as e:
            Logger.Log.error(f"优化版Selenium获取失败: {str(e)}")
            return None
        finally:
            if driver:
                try:
                    driver.quit()
                except:
                    pass

    def is_verification_page_optimized(self, driver):
        """检查是否为验证页面（优化版）"""
        try:
            page_source = driver.page_source.lower()
            verification_indicators = [
                'verify', 'captcha', '验证', '安全验证', '人机验证',
                '请完成验证', '点击验证', '滑动验证', '点选验证'
            ]
            
            for indicator in verification_indicators:
                if indicator in page_source:
                    return True
            
            # 检查weui-msg元素
            try:
                driver.find_element(By.XPATH, '//div[@class="weui-msg"]')
                return True
            except:
                pass
            
            return False
        except:
            return False

    def handle_verification_once(self, driver):
        """只尝试一次验证，失败后跳过"""
        from selenium.webdriver.common.by import By
        import time
        
        try:
            # 调试：打印页面标题和URL
            Logger.Log.info(f"验证页面标题: {driver.title}")
            Logger.Log.info(f"验证页面URL: {driver.current_url}")
            
            # 检查是否在验证页面 - 用户指定的方法
            try:
                weui_msg_element = driver.find_element(By.XPATH, '//div[@class="weui-msg"]')
                Logger.Log.info("检测到验证页面元素: //div[@class='weui-msg']")
            except:
                Logger.Log.info("未检测到验证页面元素，可能不是验证页面")
                return False
            
            # 等待页面完全加载
            time.sleep(2)
            
            # 查找用户指定的验证按钮
            try:
                verification_button = driver.find_element(By.XPATH, '//a[@class="weui-btn weui-btn_primary"]')
                Logger.Log.info("找到用户指定的验证按钮: //a[@class='weui-btn weui-btn_primary']")
                
                # 点击验证按钮
                try:
                    verification_button.click()
                    Logger.Log.info("已点击验证按钮")
                except:
                    # 尝试JavaScript点击
                    driver.execute_script("arguments[0].click();", verification_button)
                    Logger.Log.info("已使用JavaScript点击验证按钮")
                
                # 等待验证结果
                max_wait_time = 20
                start_time = time.time()
                
                while time.time() - start_time < max_wait_time:
                    time.sleep(1)
                    
                    # 检查是否成功进入文章页面
                    if self.is_article_page(driver):
                        Logger.Log.info("验证成功，已进入文章页面")
                        return True
                    
                    # 检查是否还在验证页面
                    try:
                        driver.find_element(By.XPATH, '//div[@class="weui-msg"]')
                        Logger.Log.info("仍在验证页面，继续等待...")
                        
                        # 检查是否有其他验证方式
                        try:
                            # 检查滑块验证
                            slider_elements = driver.find_elements(By.XPATH, '//*[contains(text(), "滑块") or contains(text(), "滑动") or contains(text(), "slider")]')
                            if slider_elements:
                                Logger.Log.warning("检测到滑块验证，跳过")
                                return False
                        except:
                            pass
                        
                    except:
                        # 不再有验证元素，可能已经成功
                        Logger.Log.info("验证元素已消失，可能验证成功")
                        return True
                
                Logger.Log.warning(f"验证超时，等待了{max_wait_time}秒仍未成功")
                return False
                
            except Exception as e:
                Logger.Log.error(f"查找验证按钮失败: {str(e)}")
                return False
                
        except Exception as e:
            Logger.Log.error(f"验证过程出错: {str(e)}")
            return False

    def is_migration_page(self, driver):
        """检查是否为公众号迁移页面"""
        try:
            # 方法1：检查页面标题
            page_title = driver.title
            if "账号已迁移" in page_title:
                Logger.Log.info("通过页面标题检测到公众号迁移页面")
                return True
            
            # 方法2：检查页面源码中的关键词
            page_source = driver.page_source
            migration_indicators = ['weui-msg__text-area', 'weui-msg__title', '账号已迁移', '迁移']
            for indicator in migration_indicators:
                if indicator in page_source:
                    Logger.Log.info(f"通过页面源码检测到迁移关键词: {indicator}")
                    return True
            
            # 方法3：尝试查找迁移页面的特定元素
            try:
                migration_element = driver.find_element(By.XPATH, '//div[@class="weui-msg__text-area"]/h2[@class="weui-msg__title"]')
                if migration_element:
                    Logger.Log.info("通过XPath检测到公众号迁移页面元素")
                    return True
            except:
                pass
                
            # 方法4：尝试查找迁移链接
            try:
                migration_link = driver.find_element(By.XPATH, '//div[@class="weui-msg__opr-area"]/p/a[1]')
                if migration_link:
                    Logger.Log.info("通过迁移链接检测到公众号迁移页面")
                    return True
            except:
                pass
                
        except Exception as e:
            Logger.Log.error(f"检查迁移页面时出错: {str(e)}")
        
        return False

    def handle_migration_page(self, driver):
        """处理公众号迁移页面"""
        from selenium.webdriver.common.by import By
        
        try:
            Logger.Log.info("开始处理公众号迁移页面...")
            
            # 查找迁移链接
            migration_link = driver.find_element(By.XPATH, '//div[@class="weui-msg__opr-area"]/p/a[1]')
            
            if migration_link and migration_link.is_displayed() and migration_link.is_enabled():
                Logger.Log.info(f"找到迁移链接: {migration_link.text}")
                
                # 获取链接的href属性
                href = migration_link.get_attribute('href')
                if href:
                    Logger.Log.info(f"迁移链接地址: {href}")
                    
                    # 直接访问迁移后的URL
                    driver.get(href)
                    Logger.Log.info("已跳转到迁移后的页面")
                    
                    # 等待页面加载
                    time.sleep(random.uniform(3, 5))
                    
                    # 迁移后检查页面类型
                    Logger.Log.info("迁移后检查页面类型...")
                    
                    # 首先检查是否是验证页面
                    if self.is_verification_page_optimized(driver):
                        Logger.Log.info("迁移后检测到验证页面，尝试验证...")
                        if self.handle_verification_once(driver):
                            Logger.Log.info("迁移后验证成功，继续检查...")
                            time.sleep(random.uniform(8, 15))  # 验证后等待更长时间
                        else:
                            Logger.Log.warning("迁移后验证失败，跳过此URL")
                            return False
                    
                    # 检查是否成功进入文章页面
                    if self.is_article_page(driver):
                        Logger.Log.info("迁移成功，已进入文章页面")
                        return True
                    else:
                        Logger.Log.warning("迁移后仍未进入文章页面")
                        return False
                else:
                    Logger.Log.warning("迁移链接没有href属性")
                    return False
            else:
                Logger.Log.warning("未找到可用的迁移链接")
                return False
                
        except Exception as e:
            Logger.Log.error(f"处理迁移页面时出错: {str(e)}")
            return False

    def extract_article_data_optimized(self, driver):
        """提取文章数据（优化版）"""
        from selenium.webdriver.common.by import By
        
        try:
            Logger.Log.info("开始提取文章数据...")
            
            # 获取标题 - 使用原来工作的简单逻辑
            title = ""
            try:
                title_element = driver.find_element(By.CSS_SELECTOR, 'meta[property="og:title"]')
                title = title_element.get_attribute('content')
                # 清理标题
                title = self.clean_text(title)
                Logger.Log.info(f"成功获取标题: {title[:50]}...")
            except:
                Logger.Log.warning("获取标题失败")
            
            # 获取内容 - 使用原来工作的简单逻辑
            content = ""
            try:
                content_element = driver.find_element(By.ID, 'js_content')
                content = content_element.text
                # 清理内容
                content = self.clean_text(content)
                Logger.Log.info(f"成功获取内容，长度: {len(content)}")
            except:
                Logger.Log.warning("获取内容失败")
            
            # 获取发布时间（可选）
            publish_time = ""
            try:
                scripts = driver.find_elements(By.TAG_NAME, 'script')
                for script in scripts:
                    script_text = script.get_attribute('innerHTML')
                    if 'var createTime' in script_text:
                        pattern = re.compile(r'.*(var createTime\s*=\s*\'(.*)\'\s*);.*', re.MULTILINE | re.DOTALL)
                        match = pattern.search(script_text)
                        if match:
                            publish_time = CommentUtils.Utils.date_format(match.group(2))
                            break
            except:
                pass
            
            # 获取图片（可选）
            imgs = ""
            try:
                images = driver.find_elements(By.CSS_SELECTOR, '#js_content img')
                img_urls = []
                for img in images:
                    src = img.get_attribute('data-src') or img.get_attribute('src')
                    if src:
                        img_urls.append(src)
                imgs = ','.join(img_urls)
            except:
                pass
            
            result = {
                'title': title,
                'content': content,
                'publishTime': publish_time,
                'imgs': imgs
            }
            
            Logger.Log.info(f"数据提取完成 - 标题长度: {len(title)}, 内容长度: {len(content)}")
            return result
            
        except Exception as e:
            Logger.Log.error(f"提取文章数据失败: {str(e)}")
            return None

# 兼容原有接口
class Fun:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = WechatLookBackAdvanced()
        return cls._instance
    
    @classmethod
    def get_data(cls, msg):
        instance = cls()
        return instance.get_data(msg) 