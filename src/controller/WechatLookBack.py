# -*- coding: utf-8 -*-
import json
import random
import re
import time
import urllib.parse
from urllib.parse import urlparse, parse_qs
from typing import Optional, Dict, Any

import requests
from bs4 import BeautifulSoup

from src.log import Logger
from src.utils import RabbitMQUtils, CommentUtils


class Fun:

    @classmethod
    def get_data(cls, msg):
        try:
            max_link = 5
            while max_link > 0:
                kv2 = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                }
                r = requests.get(msg['url'], timeout=30, headers=kv2)
                r.raise_for_status()
                r.encoding = r.apparent_encoding
                soup = BeautifulSoup(r.text, "html.parser")

                # 检查是否遇到验证页面
                if '完成验证后即可继续访问' in r.text or '当前环境异常' in r.text:
                    Logger.Log.warning(f"遇到验证页面，等待后重试: {msg['url']}")
                    time.sleep(random.randrange(30, 60))  # 增加等待时间
                    max_link -= 1
                    continue

                data = {
                    'taskSid': msg['taskSid'],
                    'itemSid': msg['itemSid'],
                    'dataType': 'lookback_wechat',
                    'publishTime': '',
                    'title': '',
                    'content': '',
                    'imgs': ''
                }

                if soup.find(attrs={'class': 'weui-msg__title warn'}) is not None and \
                        '该内容已被发布者删除' in soup.find(attrs={'class': 'weui-msg__title warn'}).text:
                    data.update({'content': '该内容已被发布者删除'})
                    max_link = 0
                else:
                    try:
                        soup.find(attrs={'property': 'og:title'}).attrs['content']
                    except:
                        time.sleep(3)
                        max_link -= 1
                        continue
                    max_link = 0
                    data.update({'title': soup.find(attrs={'property': 'og:title'}).attrs['content']})
                    pattern = re.compile(r'.*(var createTime\s*=\s*\'(.*)\'\s*);.*', re.MULTILINE | re.DOTALL)
                    scripts = soup.findAll('script')
                    for script in scripts:
                        if 'var createTime' in script.text:
                            if pattern.search(script.text) is None:
                                continue
                            else:
                                data.update({'publishTime': CommentUtils.Utils.date_format(pattern.search(script.text).group(2))})
                                break
                    data.update({'content': soup.find('div', {'id': 'js_content'}).get_text(separator='\n')})
                    images = soup.find('div', {'id': 'js_content'}).findAll('img')
                    imgs = ','.join([item.attrs['data-src'] for item in images])
                    data.update({'imgs': imgs})
                RabbitMQUtils.Helper.push_wechat_lookback_result(data)
                Logger.Log.info("result:" + json.dumps(data, ensure_ascii=False))
                time.sleep(random.randrange(14, 21))
        except Exception as e:
            time.sleep(5)
            if 'Invalid URL' in e.__str__() or 'Max retries exceeded with url' in e.__str__():
                raise Exception('Invalid URL')
            else:
                raise e
        return True
