# -*- coding: utf-8 -*-
import json
import random
import re
import time
import urllib.parse
from urllib.parse import urlparse, parse_qs
from typing import Optional, Dict, Any

import requests
from bs4 import BeautifulSoup

from src.log import Logger
from src.utils import RabbitMQUtils, CommentUtils


class Fun:

    @classmethod
    def setup_advanced_session(cls):
        """设置高级请求会话 - 参考WechatLookBackAdvanced"""
        session = requests.Session()

        # 使用更真实的请求头
        user_agents = [
            'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.28(0x18001c2f) NetType/WIFI Language/zh_CN',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]

        session.headers.update({
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'Referer': 'https://mp.weixin.qq.com/',
            'Origin': 'https://mp.weixin.qq.com'
        })

        return session

    @classmethod
    def fix_encoding_and_clean_text(cls, text):
        """修复编码并清理文本 - 参考WechatLookBackAdvanced"""
        if not text:
            return ""

        try:
            # 修复编码
            if isinstance(text, str) and any('\u4e00' <= char <= '\u9fff' for char in text):
                # 已经是正确的中文字符串
                pass
            elif isinstance(text, bytes):
                try:
                    text = text.decode('utf-8')
                except:
                    try:
                        text = text.decode('gbk')
                    except:
                        text = text.decode('utf-8', errors='ignore')

            # 处理HTML实体
            try:
                import html
                text = html.unescape(text)
            except:
                pass

            # 移除多余的空白字符和控制字符
            text = re.sub(r'\s+', ' ', text.strip())
            text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)

            return text
        except Exception as e:
            Logger.Log.error(f"文本处理失败: {str(e)}")
            return str(text) if text else ""

    @classmethod
    def advanced_get_wechat_content(cls, url):
        """使用高级方法获取微信公众号内容 - 参考WechatLookBackAdvanced"""
        try:
            # 设置高级会话
            session = cls.setup_advanced_session()

            # 确保使用HTTPS
            if url.startswith('http://'):
                url = url.replace('http://', 'https://')

            Logger.Log.info(f"使用高级方法访问URL: {url}")

            # 发送请求
            response = session.get(url, timeout=30, allow_redirects=True)
            response.raise_for_status()

            # 改进编码处理
            try:
                # 尝试多种编码方式
                encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'big5']
                for encoding in encodings_to_try:
                    try:
                        decoded_content = response.content.decode(encoding)
                        if any('\u4e00' <= char <= '\u9fff' for char in decoded_content):
                            response.encoding = encoding
                            Logger.Log.info(f"使用编码: {encoding}")
                            break
                    except:
                        continue
                else:
                    response.encoding = response.apparent_encoding or 'utf-8'
            except:
                response.encoding = 'utf-8'

            # 检查是否被重定向到验证页面
            if 'verify' in response.url.lower() or 'captcha' in response.url.lower():
                Logger.Log.warning(f"被重定向到验证页面: {response.url}")
                return None

            # 检查页面内容是否包含验证相关文本
            response_text = response.text.lower()
            verification_keywords = ['secitptpage/verify', 'weui-msg', '验证', '安全验证', '人机验证', '请完成验证', 'verify', 'captcha']

            # 更精确的验证页面检测
            is_verification_page = False
            if 'secitptpage/verify' in response_text:
                is_verification_page = True
                Logger.Log.warning("检测到验证页面(secitptpage/verify)")
            elif 'weui-msg' in response_text and len(response.text) < 20000:
                is_verification_page = True
                Logger.Log.warning("检测到消息页面(weui-msg)")
            elif any(keyword in response_text for keyword in ['完成验证后即可继续访问', '当前环境异常']):
                is_verification_page = True
                Logger.Log.warning("检测到验证提示文字")

            if is_verification_page:
                Logger.Log.warning("遇到验证页面，返回None")
                return None

            soup = BeautifulSoup(response.text, "html.parser")

            # 检查是否被删除
            deleted_element = soup.find(attrs={'class': 'weui-msg__title warn'})
            if deleted_element and '该内容已被发布者删除' in deleted_element.text:
                return {'content': '该内容已被发布者删除'}

            # 获取标题
            title = ''
            try:
                title_element = soup.find(attrs={'property': 'og:title'})
                if title_element:
                    title = title_element.attrs.get('content', '')
                else:
                    # 备用方案：从页面标题获取
                    title_element = soup.find('title')
                    if title_element:
                        title = title_element.text.strip()

                title = cls.fix_encoding_and_clean_text(title)
                Logger.Log.info(f"成功获取标题: {title[:50]}...")
            except Exception as e:
                Logger.Log.error(f"获取标题失败: {str(e)}")
                title = ''

            # 获取发布时间
            publish_time = ''
            try:
                time_patterns = [
                    r'var createTime\s*=\s*[\'"]([^\'"]+)[\'"]',
                    r'publish_time[\'"]?\s*:\s*[\'"]([^\'"]+)[\'"]',
                    r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})',
                    r'(\d{4}年\d{1,2}月\d{1,2}日)'
                ]

                scripts = soup.findAll('script')
                for script in scripts:
                    if script.string:
                        script_text = script.string
                        for pattern in time_patterns:
                            match = re.search(pattern, script_text)
                            if match:
                                try:
                                    publish_time = CommentUtils.Utils.date_format(match.group(1))
                                    Logger.Log.info(f"成功获取发布时间: {publish_time}")
                                    break
                                except:
                                    continue
                        if publish_time:
                            break
            except Exception as e:
                Logger.Log.error(f"获取发布时间失败: {str(e)}")
                publish_time = ''

            # 获取正文内容
            content = ''
            try:
                content_element = soup.find('div', {'id': 'js_content'})
                if content_element:
                    # 移除脚本和样式标签
                    for script in content_element(["script", "style"]):
                        script.decompose()
                    content = content_element.get_text(separator='\n', strip=True)
                    content = cls.fix_encoding_and_clean_text(content)
                    Logger.Log.info(f"成功获取内容，长度: {len(content)}")
                else:
                    Logger.Log.warning("未找到内容元素")
            except Exception as e:
                Logger.Log.error(f"获取正文内容失败: {str(e)}")
                content = ''

            # 获取图片
            imgs = ''
            try:
                if content_element:
                    images = content_element.findAll('img')
                    img_urls = []
                    for img in images:
                        src = img.attrs.get('data-src') or img.attrs.get('src')
                        if src and not src.startswith('data:'):
                            img_urls.append(src)
                    imgs = ','.join(img_urls)
                    Logger.Log.info(f"成功获取图片数量: {len(img_urls)}")
            except Exception as e:
                Logger.Log.error(f"获取图片失败: {str(e)}")
                imgs = ''

            return {
                'title': title,
                'publishTime': publish_time,
                'content': content,
                'imgs': imgs
            }

        except Exception as e:
            Logger.Log.error(f"高级获取方法失败: {str(e)}")
            return None

    @classmethod
    def get_data(cls, msg):
        try:
            max_link = 5
            while max_link > 0:
                # 使用高级方法获取内容
                advanced_result = cls.advanced_get_wechat_content(msg['url'])

                # 初始化返回数据结构（保持原有格式）
                data = {
                    'taskSid': msg['taskSid'],
                    'itemSid': msg['itemSid'],
                    'dataType': 'lookback_wechat',
                    'publishTime': '',
                    'title': '',
                    'content': '',
                    'imgs': ''
                }

                if advanced_result is None:
                    # 高级方法失败，可能遇到验证页面，使用原有的重试逻辑
                    Logger.Log.warning(f"高级方法失败，等待后重试: {msg['url']}")
                    time.sleep(random.randrange(30, 60))
                    max_link -= 1
                    continue
                elif advanced_result.get('content') == '该内容已被发布者删除':
                    # 内容已被删除
                    data.update({'content': '该内容已被发布者删除'})
                    max_link = 0
                else:
                    # 成功获取内容，填充数据
                    max_link = 0
                    data.update({
                        'title': advanced_result.get('title', ''),
                        'publishTime': advanced_result.get('publishTime', ''),
                        'content': advanced_result.get('content', ''),
                        'imgs': advanced_result.get('imgs', '')
                    })

                    Logger.Log.info(f"成功获取数据 - 标题: {data['title'][:50]}..., 内容长度: {len(data['content'])}")

                # 保持原有的业务逻辑：推送结果到消息队列
                RabbitMQUtils.Helper.push_wechat_lookback_result(data)
                Logger.Log.info("result:" + json.dumps(data, ensure_ascii=False))

                # 保持原有的等待时间
                time.sleep(random.randrange(14, 21))

        except Exception as e:
            time.sleep(5)
            if 'Invalid URL' in e.__str__() or 'Max retries exceeded with url' in e.__str__():
                raise Exception('Invalid URL')
            else:
                raise e
        return True
