# -*- coding: utf-8 -*-
import datetime
import json
import random
import time

import requests as requests
from bs4 import BeautifulSoup
import re

from src.log import Logger
from src.utils import RabbitMQUtils


class Fun:

    @classmethod
    def get_data(cls, msg):
        try:
            kv2 = {'User-agent': 'Mozilla/6.0'}
            r = requests.get(msg['url'], timeout=30, headers=kv2)
            r.raise_for_status()  # 如果状态不是200，引发HTTPError异常。
            r.encoding = r.apparent_encoding  # 从内容中分析，修正代码的编码方式。
            soup = BeautifulSoup(r.text, "html.parser")
            data = {
                'taskSid': msg['taskSid'],
                'itemSid': msg['itemSid'],
                'dataType': 'lookback_webo',
                'publishTime': '',
                'title': '',
                'content': '',
                'imgs': ''
            }
            if soup.find('img', attrs={'src': '//h5.sinaimg.cn/upload/2016/04/11/319/h5-404.png'}):
                data.update({'content': re.sub('\n', '', soup.find('div', attrs={'class': 'h5-4box'}).get_text(separator='\n'))})
            else:
                pattern = re.compile(r'.*(var \$render_data =\s*(\[{.*?}\])).*', re.MULTILINE | re.DOTALL)
                script = soup.find("script", text=pattern)
                content_json = json.loads(pattern.search(script.text).group(2))
                try:
                    data.update({'publishTime': datetime.datetime.strptime(
                        content_json[0]['status']['created_at'],
                        '%a %b %d %H:%M:%S %z %Y').strftime('%Y-%m-%d %H:%M:%S')})
                except Exception as e:
                    Logger.Log.error(e.__str__())
                text_html = '<div>%s</div>' % content_json[0]['status']['text']
                soup = BeautifulSoup(text_html, "html.parser")
                images = soup.findAll('img')
                imgs = ','.join([item.attrs['src'] for item in images])
                data.update({'imgs': imgs})
                data.update({'content': soup.get_text(separator='\n')})
            RabbitMQUtils.Helper.push_webo_lookback_result(data)
            Logger.Log.info("result:" + json.dumps(data, ensure_ascii=False))
            time.sleep(random.randrange(14, 21))
        except Exception as e:
            time.sleep(5)
            if 'Invalid URL' in e.__str__() or 'Max retries exceeded with url' in e.__str__():
                raise Exception('Invalid URL')
            else:
                raise e
        return True
