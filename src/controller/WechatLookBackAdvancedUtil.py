# -*- coding: utf-8 -*-
import os
import sys
import time
import random
import requests
import json
import re
import urllib.parse
from urllib.parse import urlparse, parse_qs
from typing import Optional, Dict, Any

import requests
from bs4 import BeautifulSoup
from selenium.webdriver.common.by import By

from src.log import Logger
from src.utils import CommentUtils


class WechatLookBackAdvancedUtil:
    """微信公众号高级获取工具类 - 完全按照WechatLookBackAdvanced原始逻辑"""

    def __init__(self):
        self.session = requests.Session()
        self.proxy_list = []
        self.current_proxy_index = 0
        self.request_count = 0
        self.last_request_time = 0
        self.setup_session()
        self.load_proxies()

    @classmethod
    def get_wechat_content(cls, url: str, auto_init_logger: bool = True) -> Optional[Dict[str, Any]]:
        """
        获取微信公众号内容的主要方法 - 完全按照原始逻辑

        Args:
            url: 微信公众号文章URL
            auto_init_logger: 是否自动初始化Logger（默认True）

        Returns:
            包含title, content, publishTime, imgs字段的字典，失败返回None
        """
        try:
            # 自动初始化Logger（如果需要）
            if auto_init_logger and not Logger.Log.logger.handlers:
                Logger.Log.init(printflag=True, level='info')
                Logger.Log.info("🔧 Logger已自动初始化，启用控制台输出")

            # 创建实例并获取数据
            instance = cls()
            msg = {'url': url}
            data = instance.get_data(msg)

            if data:
                return {
                    'title': data.get('title', ''),
                    'content': data.get('content', ''),
                    'publishTime': data.get('publishTime', ''),
                    'imgs': data.get('imgs', '')
                }
            return None

        except Exception as e:
            Logger.Log.error(f"获取微信内容失败: {str(e)}")
            return None

    def get_data(self, msg):
        """主要的数据获取方法 - 优先使用requests，失败时使用Selenium"""
        try:
            url = msg['url']
            max_retries = 2  # 减少重试次数，避免多次打开网页

            for attempt in range(max_retries):
                try:
                    Logger.Log.info(f"尝试第{attempt + 1}次获取: {url}")

                    # 优先使用requests直接访问
                    Logger.Log.info("尝试使用requests直接访问微信公众号")
                    data = self.get_data_with_requests(url)
                    if data:
                        Logger.Log.info("requests访问成功")
                        break

                    # 如果requests失败，使用Selenium
                    Logger.Log.info("requests访问失败，尝试使用Selenium")
                    data = self.get_data_with_selenium_fallback(url)
                    if data:
                        Logger.Log.info("Selenium访问成功")
                        break

                    # 如果失败，等待后重试
                    if attempt < max_retries - 1:
                        wait_time = random.uniform(10, 20)
                        Logger.Log.info(f"等待{wait_time:.1f}秒后重试...")
                        time.sleep(wait_time)

                except Exception as e:
                    Logger.Log.error(f"第{attempt + 1}次尝试失败: {str(e)}")
                    if attempt < max_retries - 1:
                        time.sleep(random.uniform(30, 60))  # 增加等待时间
                    else:
                        raise e

            if not data:
                raise Exception("所有获取方法都失败")

            return data

        except Exception as e:
            Logger.Log.error(f"获取数据失败: {str(e)}")
            if 'Invalid URL' in str(e) or 'Max retries exceeded with url' in str(e):
                raise Exception('Invalid URL')
            else:
                raise e

        return None

    def get_data_with_requests(self, url):
        """使用requests直接访问微信公众号"""
        try:
            # 添加微信特定头部
            self.add_wechat_headers(url)

            # 模拟人类行为
            self.simulate_human_behavior()

            # 获取代理并设置
            proxy = self.get_next_proxy()
            self.setup_proxy_for_requests(proxy)

            # 发送请求
            response = self.session.get(
                url,
                timeout=30,
                allow_redirects=True
            )
            response.raise_for_status()

            # 处理编码
            try:
                response.encoding = response.apparent_encoding
            except:
                response.encoding = 'utf-8'

            # 检查是否被重定向到验证页面
            if 'verify' in response.url.lower() or 'captcha' in response.url.lower():
                Logger.Log.warning(f"被重定向到验证页面: {response.url}")
                return None

            # 检查页面内容是否包含验证相关文本
            response_text = response.text.lower()
            verification_keywords = ['验证', '安全验证', '人机验证', '请完成验证', '点击验证', '滑动验证', '点选验证', 'verify', 'captcha']
            if any(keyword in response_text for keyword in verification_keywords):
                Logger.Log.warning("页面包含验证内容，需要Selenium处理")
                return None

            soup = BeautifulSoup(response.text, "html.parser")

            # 检查是否被删除
            if soup.find(attrs={'class': 'weui-msg__title warn'}) is not None and \
                    '该内容已被发布者删除' in soup.find(attrs={'class': 'weui-msg__title warn'}).text:
                return {'content': '该内容已被发布者删除'}

            # 检查是否被限制访问
            if soup.find(text=re.compile(r'访问过于频繁|访问受限|验证码')):
                Logger.Log.warning("访问被限制，需要验证")
                return None

            # 获取标题
            title = ''
            try:
                title_element = soup.find(attrs={'property': 'og:title'})
                if title_element:
                    title = title_element.attrs['content']
                else:
                    # 备用方案：从页面标题获取
                    title_element = soup.find('title')
                    if title_element:
                        title = title_element.text.strip()

                # 清理标题
                title = self.clean_text(title)
            except Exception as e:
                Logger.Log.error(f"获取标题失败: {str(e)}")
                title = ''

            # 获取发布时间
            publish_time = ''
            try:
                # 多种时间格式的正则表达式
                time_patterns = [
                    r'var createTime\s*=\s*[\'"]([^\'"]+)[\'"]',
                    r'publish_time[\'"]?\s*:\s*[\'"]([^\'"]+)[\'"]',
                    r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})',
                    r'(\d{4}年\d{1,2}月\d{1,2}日)'
                ]

                scripts = soup.findAll('script')
                for script in scripts:
                    script_text = script.text
                    for pattern in time_patterns:
                        match = re.search(pattern, script_text)
                        if match:
                            try:
                                publish_time = CommentUtils.Utils.date_format(match.group(1))
                                break
                            except:
                                continue
                    if publish_time:
                        break
            except Exception as e:
                Logger.Log.error(f"获取发布时间失败: {str(e)}")
                publish_time = ''

            # 获取正文内容
            content = ''
            try:
                content_element = soup.find('div', {'id': 'js_content'})
                if content_element:
                    # 移除脚本和样式标签
                    for script in content_element(["script", "style"]):
                        script.decompose()
                    content = content_element.get_text(separator='\n', strip=True)
                    # 清理内容
                    content = self.clean_text(content)
            except Exception as e:
                Logger.Log.error(f"获取正文内容失败: {str(e)}")
                content = ''

            # 获取图片
            imgs = ''
            try:
                if content_element:
                    images = content_element.findAll('img')
                    img_urls = []
                    for img in images:
                        src = img.attrs.get('data-src') or img.attrs.get('src')
                        if src and not src.startswith('data:'):
                            img_urls.append(src)
                    imgs = ','.join(img_urls)
            except Exception as e:
                Logger.Log.error(f"获取图片失败: {str(e)}")
                imgs = ''

            data = {
                'title': title,
                'publishTime': publish_time,
                'content': content,
                'imgs': imgs
            }

            # 验证数据
            if not self.validate_data(data):
                Logger.Log.warning("数据验证失败，可能获取不完整")

            return data

        except Exception as e:
            error_type = self.handle_network_errors(e)
            Logger.Log.error(f"requests获取方法失败: {str(e)} (错误类型: {error_type})")
            return None

    def get_data_with_selenium_fallback(self, url):
        """Selenium备用方案"""
        try:
            from selenium import webdriver
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.common.action_chains import ActionChains

            Logger.Log.info("使用Selenium备用方案")

             # 使用Chrome浏览器
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options

            chrome_options = Options()
             # chrome_options.add_argument('--headless')  # 暂时不使用headless模式便于调试
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')

             # 添加更多反检测参数
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

             # 添加更真实的User-Agent
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

             # 禁用WebDriver检测
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')

            # 添加代理支持
            proxy = self.get_next_proxy()
            if proxy:
                chrome_options.add_argument(f'--proxy-server={proxy}')
                Logger.Log.info(f"Selenium使用代理: {proxy}")

            # 随机User-Agent
            user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            ]
            chrome_options.add_argument(f'--user-agent={random.choice(user_agents)}')

            # 反检测参数
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 使用当前目录的ChromeDriver
            import os
            chromedriver_path = 'chromedriver-mac-arm64/chromedriver'
            chrome_options.binary_location = r"/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
            # Selenium 4.x版本使用Service
            from selenium.webdriver.chrome.service import Service
            service = Service(chromedriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
            Logger.Log.info("使用Chrome浏览器（本地ChromeDriver）")

            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            try:
                driver.get(url)
                WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                time.sleep(random.uniform(2, 5))

                # 检查并处理验证页面
                if self.handle_verification_page(driver):
                    Logger.Log.info("验证页面处理完成，重新获取页面内容")
                    time.sleep(random.uniform(3, 6))

                # 检查并处理公众号迁移页面
                if self.is_migration_page(driver):
                    Logger.Log.info("检测到公众号迁移页面，尝试点击迁移链接...")
                    if self.handle_migration_page(driver):
                        Logger.Log.info("迁移页面处理完成，重新获取页面内容")
                        time.sleep(random.uniform(3, 6))

                # 检查是否被删除
                try:
                    deleted_element = driver.find_element(By.CLASS_NAME, 'weui-msg__title.warn')
                    if '该内容已被发布者删除' in deleted_element.text:
                        return {'content': '该内容已被发布者删除'}
                except:
                    pass

                # 获取标题
                try:
                    title_element = driver.find_element(By.CSS_SELECTOR, 'meta[property="og:title"]')
                    title = title_element.get_attribute('content')
                    # 清理标题
                    title = self.clean_text(title)
                except:
                    title = ''

                # 获取发布时间
                publish_time = ''
                try:
                    scripts = driver.find_elements(By.TAG_NAME, 'script')
                    for script in scripts:
                        script_text = script.get_attribute('innerHTML')
                        if 'var createTime' in script_text:
                            pattern = re.compile(r'.*(var createTime\s*=\s*\'(.*)\'\s*);.*', re.MULTILINE | re.DOTALL)
                            match = pattern.search(script_text)
                            if match:
                                publish_time = CommentUtils.Utils.date_format(match.group(2))
                                break
                except:
                    pass

                # 获取正文内容
                try:
                    content_element = driver.find_element(By.ID, 'js_content')
                    content = content_element.text
                    # 清理内容
                    content = self.clean_text(content)
                except:
                    content = ''

                # 获取图片
                imgs = ''
                try:
                    images = driver.find_elements(By.CSS_SELECTOR, '#js_content img')
                    img_urls = []
                    for img in images:
                        src = img.get_attribute('data-src') or img.get_attribute('src')
                        if src:
                            img_urls.append(src)
                    imgs = ','.join(img_urls)
                except:
                    pass

                return {
                    'title': title,
                    'publishTime': publish_time,
                    'content': content,
                    'imgs': imgs
                }

            finally:
                driver.quit()

        except ImportError:
            Logger.Log.error("Selenium未安装，无法使用备用方案")
            return None
        except Exception as e:
            Logger.Log.error(f"Selenium获取数据失败: {str(e)}")
            return None

    def handle_verification_page(self, driver):
        """处理验证页面，持续点击验证按钮直到成功跳转到文章页面"""
        try:
            from selenium.webdriver.common.by import By

            # 检查是否在验证页面
            current_url = driver.current_url
            page_source = driver.page_source.lower()

            Logger.Log.info(f"当前URL: {current_url}")
            Logger.Log.info(f"页面标题: {driver.title}")

            # 检查各种验证页面的标识
            verification_indicators = [
                'verify', 'captcha', '验证', '安全验证', '人机验证',
                '请完成验证', '点击验证', '滑动验证', '点选验证'
            ]

            is_verification_page = False
            found_indicators = []
            for indicator in verification_indicators:
                if indicator in page_source:
                    is_verification_page = True
                    found_indicators.append(indicator)

            if found_indicators:
                Logger.Log.info(f"检测到验证页面，标识: {', '.join(found_indicators)}")

            # 检查是否有weui-msg元素（用户提供的验证页面标识）
            try:
                weui_msg_element = driver.find_element(By.XPATH, '//div[@class="weui-msg"]')
                if weui_msg_element:
                    is_verification_page = True
                    Logger.Log.info("检测到weui-msg验证页面元素")
                    Logger.Log.info(f"weui-msg元素文本: {weui_msg_element.text}")
            except Exception as e:
                Logger.Log.info(f"未找到weui-msg元素: {str(e)}")

            if not is_verification_page:
                Logger.Log.info("当前页面不是验证页面")
                return False

            Logger.Log.info("开始处理验证页面...")

            # 等待页面完全加载
            time.sleep(random.uniform(2, 4))

            # 持续验证直到成功跳转
            max_verification_attempts = 10  # 最多尝试10次验证
            verification_attempts = 0

            while verification_attempts < max_verification_attempts:
                verification_attempts += 1
                Logger.Log.info(f"第{verification_attempts}次验证尝试...")

                # 检查是否已经跳转到文章页面
                if self.is_article_page(driver):
                    Logger.Log.info("已成功跳转到文章页面")
                    return True

                # 尝试点击验证按钮
                verification_clicked = False

                try:
                    # 查找验证按钮 - 使用用户提供的XPath
                    verify_button = driver.find_element(By.XPATH, '//a[@class="weui-btn weui-btn_primary"]')

                    if verify_button and verify_button.is_displayed() and verify_button.is_enabled():
                        Logger.Log.info(f"找到验证按钮: {verify_button.text}")

                        # 滚动到元素位置
                        driver.execute_script("arguments[0].scrollIntoView(true);", verify_button)
                        time.sleep(random.uniform(0.5, 1.5))

                        # 尝试点击
                        try:
                            # 使用JavaScript点击（因为按钮没有href属性）
                            driver.execute_script("arguments[0].click();", verify_button)
                            Logger.Log.info("JavaScript点击验证按钮成功")
                            verification_clicked = True

                            # 智能等待验证完成和页面跳转
                            Logger.Log.info("等待验证完成...")

                             # 使用轮询方式检查验证状态，最多等待30秒
                            max_wait_time = 30
                            check_interval = 2
                            waited_time = 0

                            while waited_time < max_wait_time:
                                 time.sleep(check_interval)
                                 waited_time += check_interval

                                 Logger.Log.info(f"=== 验证状态检查 (第{waited_time//check_interval}次检查，已等待{waited_time}秒) ===")

                                 # 检查是否已经跳转到文章页面
                                 if self.is_article_page(driver):
                                     Logger.Log.info(f"✅ 验证完成，已跳转到文章页面 (等待了{waited_time}秒)")
                                     return True

                                 # 检查URL是否发生变化
                                 current_url = driver.current_url
                                 if 'verify' not in current_url.lower() and 'captcha' not in current_url.lower():
                                     Logger.Log.info(f"🔄 URL已跳转，验证可能完成 (等待了{waited_time}秒)")
                                     time.sleep(random.uniform(3, 5))  # 再等待一下让页面完全加载
                                     if self.is_article_page(driver):
                                         Logger.Log.info("✅ 验证完成，已跳转到文章页面")
                                         return True

                                 Logger.Log.info(f"⏳ 验证进行中... (已等待{waited_time}秒)")

                            Logger.Log.warning(f"验证超时，已等待{max_wait_time}秒")

                        except Exception as click_error:
                            Logger.Log.warning(f"点击验证按钮失败: {str(click_error)}")

                except Exception as e:
                    Logger.Log.warning(f"未找到指定的验证按钮: {str(e)}")

                # 如果这次尝试没有点击成功，等待后继续
                if not verification_clicked:
                    Logger.Log.warning(f"第{verification_attempts}次验证尝试失败，等待后重试...")
                    time.sleep(random.uniform(3, 6))

            # 如果所有尝试都失败，记录日志
            Logger.Log.warning(f"经过{max_verification_attempts}次验证尝试后仍未能成功跳转")
            return False

        except Exception as e:
            Logger.Log.error(f"处理验证页面时出错: {str(e)}")
            return False

    def is_article_page(self, driver):
        """检查是否已经跳转到文章页面"""
        try:
            from selenium.webdriver.common.by import By

            # 检查多种可能的内容元素
            content_selectors = [
                '//div[@id="js_content"]',
                '//div[contains(@class, "rich_media_content")]',
                '//div[contains(@class, "content")]',
                '//article',
                '//div[contains(@class, "article")]',
                '//div[contains(@class, "post")]'
            ]

            for selector in content_selectors:
                try:
                    element = driver.find_element(By.XPATH, selector)
                    if element and element.text.strip():
                        Logger.Log.info(f"检测到文章内容元素: {selector}")
                        return True
                except:
                    continue

            # 检查是否有文章标题
            try:
                title_element = driver.find_element(By.CSS_SELECTOR, 'meta[property="og:title"]')
                if title_element and title_element.get_attribute('content'):
                    Logger.Log.info("检测到文章标题元素")
                    return True
            except:
                pass

            # 检查URL是否包含文章标识
            current_url = driver.current_url
            if '/s?' in current_url and '__biz=' in current_url:
                Logger.Log.info("检测到文章URL特征")
                return True

            return False

        except Exception as e:
            Logger.Log.warning(f"检查文章页面时出错: {str(e)}")
            return False

    def is_migration_page(self, driver):
        """检查是否为迁移页面"""
        try:
            from selenium.webdriver.common.by import By

            # 检查迁移页面特有元素
            try:
                migration_title = driver.find_element(By.XPATH, '//div[@class="weui-msg__text-area"]/h2[@class="weui-msg__title"]')
                migration_link = driver.find_element(By.XPATH, '//div[@class="weui-msg__opr-area"]/p/a[1]')

                if migration_title and migration_link:
                    Logger.Log.info("检测到迁移页面元素")
                    Logger.Log.info(f"迁移标题: {migration_title.text}")
                    Logger.Log.info(f"迁移链接: {migration_link.get_attribute('href')}")
                    return True
            except Exception as e:
                Logger.Log.info(f"未找到迁移页面元素: {str(e)}")

            # 检查页面源码中的迁移关键词
            page_source = driver.page_source
            migration_keywords = ['账号已迁移', '迁移', '该账号已迁移']
            if any(keyword in page_source for keyword in migration_keywords):
                Logger.Log.info("检测到迁移页面关键词")
                return True

            return False
        except Exception as e:
            Logger.Log.error(f"检查迁移页面时出错: {str(e)}")
            return False

    def handle_migration_page(self, driver):
        """处理迁移页面"""
        try:
            from selenium.webdriver.common.by import By

            Logger.Log.info("开始处理迁移页面...")

            # 查找迁移链接
            migration_link = driver.find_element(By.XPATH, '//div[@class="weui-msg__opr-area"]/p/a[1]')

            if migration_link:
                href = migration_link.get_attribute('href')
                if href:
                    Logger.Log.info(f"找到迁移链接: {href}")

                    # 点击迁移链接
                    try:
                        migration_link.click()
                        Logger.Log.info("点击迁移链接成功")
                    except:
                        # 尝试JavaScript点击
                        driver.execute_script("arguments[0].click();", migration_link)
                        Logger.Log.info("使用JavaScript点击迁移链接")

                    # 等待页面跳转
                    time.sleep(random.uniform(3, 6))
                    Logger.Log.info("迁移处理完成")
                    return True
                else:
                    Logger.Log.warning("迁移链接没有href属性")
                    return False
            else:
                Logger.Log.warning("未找到迁移链接")
                return False

        except Exception as e:
            Logger.Log.error(f"迁移处理失败: {str(e)}")
            return False

    def setup_session(self):
        """设置更真实的请求会话"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ]

        self.session.headers.update({
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"'
        })

    def load_proxies(self):
        """加载代理列表"""
        try:
            from config.proxy_config import PROXY_LIST, ENABLE_PROXY
            if ENABLE_PROXY and PROXY_LIST:
                self.proxy_list = PROXY_LIST
                Logger.Log.info(f"已加载 {len(self.proxy_list)} 个代理")
            else:
                Logger.Log.info("代理未启用或代理列表为空")
        except ImportError:
            Logger.Log.warning("代理配置文件不存在，将不使用代理")

    def setup_proxy_for_requests(self, proxy):
        """为requests设置代理"""
        if proxy:
            self.session.proxies = {
                'http': proxy,
                'https': proxy
            }
            Logger.Log.info(f"使用代理: {proxy}")
        else:
            self.session.proxies = {}
            Logger.Log.info("不使用代理")

    def get_next_proxy(self):
        """获取下一个代理"""
        if not self.proxy_list:
            return None

        proxy = self.proxy_list[self.current_proxy_index]
        self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxy_list)
        return proxy

    def generate_fingerprint(self):
        """生成浏览器指纹"""
        screen_resolutions = ['1920x1080', '1366x768', '1440x900', '1536x864']
        color_depths = [24, 32]
        timezones = ['Asia/Shanghai', 'Asia/Hong_Kong']

        fingerprint = {
            'screen': random.choice(screen_resolutions),
            'colorDepth': random.choice(color_depths),
            'timezone': random.choice(timezones),
            'language': 'zh-CN',
            'platform': 'Win32'
        }

        return fingerprint

    def add_wechat_headers(self, url):
        """添加微信特定的请求头"""
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)

        # 添加微信特定的头部
        additional_headers = {
            'Referer': 'https://mp.weixin.qq.com/',
            'Origin': 'https://mp.weixin.qq.com',
            'X-Requested-With': 'XMLHttpRequest'
        }

        # 如果有特定的微信参数，添加到头部
        if '__biz' in query_params:
            additional_headers['X-Wechat-Biz'] = query_params['__biz'][0]

        self.session.headers.update(additional_headers)

    def simulate_human_behavior(self):
        """模拟人类行为"""
        # 随机延迟
        time.sleep(random.uniform(1, 3))

        # 随机更新User-Agent
        if random.random() < 0.3:
            user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ]
            self.session.headers['User-Agent'] = random.choice(user_agents)

        # 控制请求频率
        current_time = time.time()
        if current_time - self.last_request_time < 15:
            sleep_time = 15 - (current_time - self.last_request_time)
            time.sleep(sleep_time)

        self.last_request_time = time.time()
        self.request_count += 1

    def handle_network_errors(self, e):
        """处理网络错误"""
        error_msg = str(e).lower()

        if 'timeout' in error_msg:
            Logger.Log.warning("请求超时，可能需要增加超时时间")
            return 'timeout'
        elif 'connection' in error_msg:
            Logger.Log.warning("连接错误，可能是网络问题")
            return 'connection'
        elif 'ssl' in error_msg:
            Logger.Log.warning("SSL证书错误")
            return 'ssl'
        elif '403' in error_msg or 'forbidden' in error_msg:
            Logger.Log.warning("访问被禁止，可能需要更换IP或User-Agent")
            return 'forbidden'
        elif '404' in error_msg:
            Logger.Log.warning("页面不存在")
            return 'not_found'
        elif '429' in error_msg or 'too many requests' in error_msg:
            Logger.Log.warning("请求过于频繁，需要增加延迟")
            return 'rate_limit'
        else:
            Logger.Log.error(f"未知网络错误: {str(e)}")
            return 'unknown'

    def fix_encoding(self, text):
        """修复编码问题 - 简化版本"""
        if not text:
            return text

        try:
            # 如果已经是正确的中文字符串，直接返回
            if isinstance(text, str) and any('\u4e00' <= char <= '\u9fff' for char in text):
                return text

            # 如果是字节串，尝试解码
            if isinstance(text, bytes):
                try:
                    return text.decode('utf-8')
                except:
                    try:
                        return text.decode('gbk')
                    except:
                        return text.decode('utf-8', errors='ignore')

            # 如果是字符串，尝试HTML实体解码
            elif isinstance(text, str):
                try:
                    import html
                    decoded_text = html.unescape(text)
                    if decoded_text != text:
                        return decoded_text
                except:
                    pass

                return text

        except Exception as e:
            Logger.Log.error(f"编码修复失败: {str(e)}")
            return str(text) if text else ""

        return text

    def clean_text(self, text):
        """清理文本内容"""
        if not text:
            return ""

        try:
            # 修复编码
            text = self.fix_encoding(text)

            # 处理HTML实体
            try:
                import html
                text = html.unescape(text)
            except:
                pass

            # 移除多余的空白字符
            text = re.sub(r'\s+', ' ', text.strip())

            # 移除控制字符
            text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)

            # 保留中文、英文、数字、标点符号等有用字符
            text = re.sub(r'[^\w\s\u4e00-\u9fff\u3000-\u303f\uff00-\uffef\-\.\,\!\?\(\)\[\]\{\}\:\;\'\"\@\#\$\%\^\&\*\+\=\|\~`]', '', text)

            # 再次清理多余空白
            text = re.sub(r'\s+', ' ', text.strip())

            return text
        except Exception as e:
            Logger.Log.error(f"文本清理失败: {str(e)}")
            return str(text) if text else ""

    def validate_data(self, data):
        """验证获取的数据"""
        if not data:
            return False

        # 检查必要字段
        required_fields = ['title', 'content']
        for field in required_fields:
            if field not in data or not data[field]:
                Logger.Log.warning(f"缺少必要字段: {field}")
                return False

        # 检查内容长度
        if len(data.get('content', '')) < 10:
            Logger.Log.warning("内容太短，可能获取失败")
            return False

        # 检查标题长度
        if len(data.get('title', '')) < 2:
            Logger.Log.warning("标题太短，可能获取失败")
            return False

        return True

    @classmethod
    def _get_with_requests(cls, url: str) -> Optional[Dict[str, Any]]:
        """使用requests方法获取内容"""
        try:
            Logger.Log.info("📡 使用requests方法获取内容")
            
            # 设置请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.28(0x18001c2f) NetType/WIFI Language/zh_CN',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Referer': 'https://mp.weixin.qq.com/',
            }
            
            # 发送请求
            response = requests.get(url, headers=headers, timeout=30, allow_redirects=True)
            response.raise_for_status()
            
            # 处理编码
            response.encoding = response.apparent_encoding or 'utf-8'
            
            # 检查是否被重定向到验证页面
            if 'verify' in response.url.lower() or 'captcha' in response.url.lower():
                Logger.Log.warning(f"被重定向到验证页面: {response.url}")
                return None
            
            # 检查页面内容是否包含验证相关文本
            response_text = response.text.lower()
            verification_keywords = ['验证', '安全验证', '人机验证', 'verify', 'captcha']
            if any(keyword in response_text for keyword in verification_keywords):
                Logger.Log.warning("页面包含验证内容，需要Selenium处理")
                return None
            
            soup = BeautifulSoup(response.text, "html.parser")
            
            # 检查是否被删除
            deleted_element = soup.find(attrs={'class': 'weui-msg__title warn'})
            if deleted_element and '该内容已被发布者删除' in deleted_element.text:
                Logger.Log.info("检测到内容已被删除")
                return {'title': '', 'content': '该内容已被发布者删除', 'publishTime': '', 'imgs': ''}
            
            # 提取数据
            return cls._extract_data_from_soup(soup)
            
        except Exception as e:
            Logger.Log.error(f"requests方法失败: {str(e)}")
            return None
    
    @classmethod
    def _get_with_selenium(cls, url: str) -> Optional[Dict[str, Any]]:
        """使用Selenium方法获取内容"""
        driver = None
        try:
            Logger.Log.info("🤖 使用Selenium方法获取内容")
            
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.chrome.service import Service
            from selenium.webdriver.common.by import By
            
            # 设置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.28(0x18001c2f) NetType/WIFI Language/zh_CN')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 设置ChromeDriver路径
            chromedriver_path = '/Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/chromedriver-mac-arm64/chromedriver'
            chrome_options.binary_location = r"/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
            
            service = Service(chromedriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # 反检测设置
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 访问页面
            driver.get(url)
            time.sleep(random.uniform(3, 5))
            
            # 检查并处理验证页面
            if cls._handle_verification_page(driver):
                Logger.Log.info("验证页面处理完成")
                time.sleep(random.uniform(3, 5))
            
            # 检查是否被删除
            try:
                deleted_element = driver.find_element(By.CLASS_NAME, 'weui-msg__title.warn')
                if '该内容已被发布者删除' in deleted_element.text:
                    return {'title': '', 'content': '该内容已被发布者删除', 'publishTime': '', 'imgs': ''}
            except:
                pass
            
            # 提取数据
            return cls._extract_data_from_driver(driver)
            
        except ImportError:
            Logger.Log.error("Selenium未安装，无法使用Selenium方案")
            return None
        except Exception as e:
            Logger.Log.error(f"Selenium方法失败: {str(e)}")
            return None
        finally:
            if driver:
                try:
                    driver.quit()
                except:
                    pass

    @classmethod
    def _extract_data_from_soup(cls, soup) -> Dict[str, Any]:
        """从BeautifulSoup对象中提取数据"""
        try:
            # 获取标题
            title = ''
            try:
                title_element = soup.find(attrs={'property': 'og:title'})
                if title_element:
                    title = title_element.attrs['content']
                else:
                    title_element = soup.find('title')
                    if title_element:
                        title = title_element.text.strip()
                title = cls._clean_text(title)
            except Exception as e:
                Logger.Log.error(f"获取标题失败: {str(e)}")
                title = ''

            # 获取发布时间
            publish_time = ''
            try:
                scripts = soup.findAll('script')
                for script in scripts:
                    script_text = script.text
                    match = re.search(r'var createTime\s*=\s*[\'"]([^\'"]+)[\'"]', script_text)
                    if match:
                        try:
                            publish_time = CommentUtils.Utils.date_format(match.group(1))
                            break
                        except:
                            continue
            except Exception as e:
                Logger.Log.error(f"获取发布时间失败: {str(e)}")
                publish_time = ''

            # 获取正文内容
            content = ''
            try:
                content_element = soup.find('div', {'id': 'js_content'})
                if content_element:
                    for script in content_element(["script", "style"]):
                        script.decompose()
                    content = content_element.get_text(separator='\n', strip=True)
                    content = cls._clean_text(content)
            except Exception as e:
                Logger.Log.error(f"获取正文内容失败: {str(e)}")
                content = ''

            # 获取图片
            imgs = ''
            try:
                if content_element:
                    images = content_element.findAll('img')
                    img_urls = []
                    for img in images:
                        src = img.attrs.get('data-src') or img.attrs.get('src')
                        if src and not src.startswith('data:'):
                            img_urls.append(src)
                    imgs = ','.join(img_urls)
            except Exception as e:
                Logger.Log.error(f"获取图片失败: {str(e)}")
                imgs = ''

            return {
                'title': title,
                'publishTime': publish_time,
                'content': content,
                'imgs': imgs
            }

        except Exception as e:
            Logger.Log.error(f"从soup提取数据失败: {str(e)}")
            return {'title': '', 'content': '', 'publishTime': '', 'imgs': ''}

    @classmethod
    def _extract_data_from_driver(cls, driver) -> Dict[str, Any]:
        """从Selenium driver中提取数据"""
        try:
            from selenium.webdriver.common.by import By

            # 获取标题
            title = ''
            try:
                title_element = driver.find_element(By.CSS_SELECTOR, 'meta[property="og:title"]')
                title = title_element.get_attribute('content')
                title = cls._clean_text(title)
            except:
                title = ''

            # 获取发布时间
            publish_time = ''
            try:
                scripts = driver.find_elements(By.TAG_NAME, 'script')
                for script in scripts:
                    script_text = script.get_attribute('innerHTML')
                    if 'var createTime' in script_text:
                        match = re.search(r'var createTime\s*=\s*[\'"]([^\'"]+)[\'"]', script_text)
                        if match:
                            try:
                                publish_time = CommentUtils.Utils.date_format(match.group(1))
                                break
                            except:
                                continue
            except:
                pass

            # 获取正文内容
            content = ''
            try:
                content_element = driver.find_element(By.ID, 'js_content')
                content = content_element.text
                content = cls._clean_text(content)
            except:
                content = ''

            # 获取图片
            imgs = ''
            try:
                images = driver.find_elements(By.CSS_SELECTOR, '#js_content img')
                img_urls = []
                for img in images:
                    src = img.get_attribute('data-src') or img.get_attribute('src')
                    if src and not src.startswith('data:'):
                        img_urls.append(src)
                imgs = ','.join(img_urls)
            except:
                pass

            return {
                'title': title,
                'publishTime': publish_time,
                'content': content,
                'imgs': imgs
            }

        except Exception as e:
            Logger.Log.error(f"从driver提取数据失败: {str(e)}")
            return {'title': '', 'content': '', 'publishTime': '', 'imgs': ''}

    @classmethod
    def _handle_verification_page(cls, driver) -> bool:
        """处理验证页面"""
        try:
            from selenium.webdriver.common.by import By

            # 检查是否在验证页面
            current_url = driver.current_url
            page_source = driver.page_source.lower()

            verification_indicators = ['verify', 'captcha', '验证', '安全验证', '人机验证']
            is_verification_page = any(indicator in page_source for indicator in verification_indicators)

            if not is_verification_page:
                return False

            Logger.Log.info("🔍 检测到验证页面，尝试处理...")

            # 尝试点击验证按钮
            try:
                verify_button = driver.find_element(By.XPATH, '//a[@class="weui-btn weui-btn_primary"]')
                if verify_button and verify_button.is_displayed() and verify_button.is_enabled():
                    driver.execute_script("arguments[0].click();", verify_button)
                    Logger.Log.info("✅ 点击验证按钮成功")
                    time.sleep(random.uniform(5, 10))
                    return True
            except:
                pass

            return False

        except Exception as e:
            Logger.Log.error(f"处理验证页面失败: {str(e)}")
            return False

    @classmethod
    def _clean_text(cls, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""

        try:
            # 处理HTML实体
            import html
            text = html.unescape(text)

            # 移除多余的空白字符
            text = re.sub(r'\s+', ' ', text.strip())

            # 移除控制字符
            text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)

            return text
        except Exception as e:
            Logger.Log.error(f"文本清理失败: {str(e)}")
            return str(text) if text else ""
