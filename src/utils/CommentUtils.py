# -*- coding: utf-8 -*-
import base64
import datetime
import uuid
import hashlib
import json
import os
import re
import socket
from typing import List, Optional, Tuple, Union



class Utils:

    sort_id_chars = ["a", "b", "c", "d", "e", "f","g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t",
                     "u", "v", "w", "x", "y", "z", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D",
                     "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X",
                     "Y", "Z"]

    @staticmethod
    def get_sort_id(source_str=None):
        ret = ''
        if source_str is None:
            uuid_str = str(uuid.uuid4()).replace('-', '').lower()
        else:
            uuid_str = hashlib.md5(source_str.encode('utf-8')).hexdigest()
        for i in range(8):
            temp_str = uuid_str[i*4:(i*4 + 4)]
            temp_int = int(temp_str, 16)
            ret += Utils.sort_id_chars[temp_int % 62]
        return ret


    @staticmethod
    def date_format(source_date):
        if re.match(r'\d{2,4}[-/\.年月]\d{1,2}[-/\.月日]\d{1,2}[年日]*\s*\d{1,2}[:\.]\d{1,2}[:\.]*(\d{1,2})*\s*(am|pm|AM|PM|Am|Pm)*',
                    source_date):
            date_group = re.findall(r'(\d{2,4})[-/\.年月](\d{1,2})[-/\.月日](\d{1,2})[年日]*\s*'
                                    r'(\d{1,2})[:\.](\d{1,2})[:\.]*(\d{1,2})*\s*(am|pm|AM|PM|Am|Pm)*', source_date)[0]

            if len(date_group[0]) == 4:
                year = int(date_group[0])
                month = int(date_group[1])
                day = int(date_group[2])
            if len(date_group[2]) == 4:
                year = int(date_group[2])
                month = int(date_group[0])
                day = int(date_group[1])
            if len(date_group[0]) == 2 and int(date_group[0]) > 12:
                year = int(str(datetime.datetime.now().year)[0:2] + date_group[0])
                month = int(date_group[1])
                day = int(date_group[2])
            if len(date_group[0]) == 2 and len(date_group[2]) == 2 and int(date_group[0]) <=12 and int(date_group[2]) > 12:
                year = int(str(datetime.datetime.now().year)[0:2] + date_group[2])
                month = int(date_group[1])
                day = int(date_group[2])

            if date_group[5].lower() == 'pm':
                date_group = (date_group[0], date_group[1], date_group[2], date_group[3], date_group[4], '', 'pm')

            if date_group[6].lower() == 'pm':
                hour = int(date_group[3]) + 12
            else:
                hour = int(date_group[3])

            minute = int(date_group[4])
            second = 0 if date_group[5] == '' else int(date_group[5])
            date = datetime.datetime(year, month, day, hour, minute, second)
            return datetime.datetime.strftime(date, '%Y-%m-%d %H:%M:%S')



    @staticmethod
    def get_root_path():
        root_patch = os.getcwd().replace(r'\src', '')
        root_patch = root_patch.replace("\\", "/")
        return root_patch

    @staticmethod
    def get_expire(expire):
        expire = expire.lower()
        if re.match(r'\d+d', expire):
            return datetime.timedelta(days=int(re.findall(r'(\d+)d', expire)[0]))
        elif re.match(r'\d+m', expire):
            return datetime.timedelta(days=int(re.findall(r'(\d+)m', expire)[0]) * 30)
        elif re.match(r'\d+y', expire):
            return datetime.timedelta(days=int(re.findall(r'(\d+)m', expire)[0]) * 360)
        elif re.match(r'\d+', expire):
            return int(expire) * 1000
        else:
            return None

    @staticmethod
    def object_is_empty(obj, key=None):
        """
            @:param obj hahaha
            @:param key dag
        """
        if isinstance(obj, dict) and key is not None:
            if key in obj.keys() and obj[key] is not None:
                if isinstance(obj[key], str):
                    if obj[key].strip() == '':
                        return True
                    else:
                        return False
                elif isinstance(obj[key], list):
                    if len(obj[key]) > 0:
                        return False
                    else:
                        return True
                elif isinstance(obj[key], dict):
                    if len(obj[key].keys()) > 0:
                        return False
                    else:
                        return True
                elif isinstance(obj[key], tuple):
                    if len(obj[key]) > 0:
                        return False
                    else:
                        return True
                else:
                    if obj[key] is None:
                        return True
                    else:
                        return False
            else:
                return True
        elif isinstance(obj, list):
            if len(obj) > 0:
                return False
            else:
                return True
        elif isinstance(obj, dict) and key is None:
            if len(obj.keys()) > 0:
                return False
            else:
                return True
        elif isinstance(obj, str):
            if obj.strip() == '':
                return True
            else:
                return False
        elif isinstance(obj, tuple):
            if len(obj) > 0:
                return False
            else:
                return True
        else:
            if obj is None:
                return True
            else:
                return False

    @staticmethod
    def get_ram_size(ram_bytes, suffix="B"):
        """
        Scale bytes to its proper format
        e.g:
            1253656 => '1.20MB'
            1253656678 => '1.17GB'
        """
        factor = 1024
        for unit in ["", "K", "M", "G", "T", "P"]:
            if ram_bytes < factor:
                return f"{ram_bytes:.2f}{unit}{suffix}"
            ram_bytes /= factor

    @staticmethod
    def getHostIp():
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(('*******', 80))
            ip = s.getsockname()[0]
        finally:
            s.close()
        return ip

    @staticmethod
    def get_bool_str(bool_str: Optional[str], is_py_type=False):
        bool_str = bool_str.strip()
        bool_str = bool_str[0].upper() + bool_str[1:]
        try:
            if is_py_type:
                return str(eval(bool_str))
            else:
                return str(eval(bool_str)).lower()
        except:
            return False

    @staticmethod
    def getNintStr(bool_str: Optional[str]):
        if re.match(r'^[1-9]\d*$|^0$', bool_str):
            return bool_str
        else:
            return None

    @staticmethod
    def is_xpath(selector: Optional[str]):
        if re.match('^\.(?![\.|//].*).*|^/{1,2}(?!/.*).*|^@(?!@.*).*|^\./{1,1}(?!/.*).*', selector):
            return True
        else:
            return False

    @staticmethod
    def is_json_str(raw_msg: Optional[str]):
        if isinstance(raw_msg, str):  # 首先判断变量是否为字符串
            try:
                json.loads(raw_msg, encoding='utf-8')
            except ValueError:
                return False
            return True
        else:
            return False

    @staticmethod
    def get_app_oper_json():
        json_path = Utils.get_root_path() + "/resources/operates/app_operates.json"
        with open(json_path, 'r', encoding='utf-8') as load_f:
            return json.load(load_f)

    @staticmethod
    def get_json_file(json_file, dir_path=None):
        if dir_path is None:
            json_path = json_file
        else:
            json_path = dir_path + json_file
        with open(json_path, 'r', encoding='utf-8') as load_f:
            ret = json.load(load_f)
        load_f.close()
        return ret

    @staticmethod
    def md5vale(key):
        input_name = hashlib.md5()
        input_name.update(key.encode("utf-8"))
        return input_name.hexdigest()

    @staticmethod
    def getMatterByType(result, matter_type):
        return len(list(filter(
            lambda x: len(list(filter(lambda p: re.match(r'^' + matter_type + '.*$', p['code']), x['hitType']))) > 0,
            result)))

    @staticmethod
    def _getColumnIndex(ws, columnName):
        columnIndex = None
        for i in range(1, ws.max_column + 1):
            if (ws.cell(1, i).value == columnName):
                columnIndex = i
                break
        return columnIndex



    @staticmethod
    def __add_cell_value(ws, keyword, start_keyword, col_num, start_flag=[False]):
        for row in range(1, ws.max_row):
            cell_value = ws.cell(row + 1, col_num).value
            if cell_value is None or cell_value == '':
                break
            if cell_value == 'None':
                continue
            if not start_flag[0]:
                if start_keyword is None:
                    keyword.append(cell_value)
                    start_flag[0] = True
                else:
                    if cell_value != start_keyword:
                        continue
                    else:
                        keyword.append(cell_value)
                        start_flag[0] = True
                        continue
            else:
                keyword.append(cell_value)

    @staticmethod
    def parseUrl(url, url_type):
        web_treaty = re.findall('(http://|https://|ftp://).*', url)[0]
        if url_type == 'treaty':
            return web_treaty.replace('//', '')
        url_paths = url.replace(web_treaty, '').split('/')
        if url_type == 'base':
            return web_treaty + ''.join(url_paths[0])
        elif url_type == 'host':
            host = re.findall(r'(?i)^https?://(?:[\w\-]*\.)*?([\w\-]*\.(?:com\.cn|edu\.cn|org\.cn|vip\.cn|top\.cn'
                              r'|ai\.cn|cc\.cn|github\.io|gov\.cn|cn|com|net|org|vip|xin|top|club|xyz|wang|win|ai'
                              r'|tw|jp|cc|io))[\\/]*', url)
            return host[0]
        elif 'real' in url_type:
            ret_url = '/'.join(url_paths[:-1])
            if url_type == 'real':
                return web_treaty + re.sub(r'/#.*', '', ret_url)
            elif url_type == 'real_array':
                return web_treaty, re.sub(r'/#.*', '', ret_url).split('/')

    @staticmethod
    def parseImgSrc(src, current_url):
        if re.match(r'^http.*', src) or re.match(r'^ftp.*', src):
            return src
        elif src.startswith('//'):
            return Utils.parseUrl(current_url, 'treaty') + src
        elif src.startswith('/'):
            return Utils.parseUrl(current_url, 'base') + src
        elif src.startswith('#/'):
            return Utils.parseUrl(current_url, 'base') + src[1:]
        elif src.startswith('./'):
            return Utils.parseUrl(current_url, 'real') + src[1:]
        elif src.startswith('../'):
            web_treaty, real_paths = Utils.parseUrl(current_url, 'real_array')
            flag = True
            end_flag = 0
            while flag:
                if src.startswith('../'):
                    src = src.replace('../', '')
                    end_flag = end_flag - 1
                else:
                    flag = False
            return web_treaty + '/'.join(real_paths[:end_flag]) + '/' + src
        else:
            if not re.match('^data:image.*', src):
                return Utils.parseUrl(current_url, 'real') + '/' + src
            else:
                return src

