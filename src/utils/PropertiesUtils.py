# -*- coding: utf-8 -*-
import re
from configparser import ConfigParser
from src.utils import CommentUtils


class Utils:

    @staticmethod
    def get_config(props_group, props_name):
        root = CommentUtils.Utils.get_root_path()
        cp_root = ConfigParser()
        cp_ext = ConfigParser()
        cp_root.read(root + '/config/config.cfg', encoding="utf-8")
        try:
            env = cp_root.get('service', 'env')
        except Exception as e:
            if 'No option \'env\' in section: \'controller\'' in e.message:
                cp_ext = cp_root
        if env == 'test':
            cp_ext.read(root + '/config/config.test.cfg', encoding="utf-8")
        elif re.match(r'^.*\.prod$|^.*\.test$', env):
            cp_ext.read(root + '/config/config.%s.cfg' % env, encoding="utf-8")
        elif env == 'prod':
            cp_ext.read(root + '/config/config.prod.cfg', encoding="utf-8")
        else:
            cp_ext = cp_root

        prop = cp_root.get(props_group, props_name)
        if re.match(r'^\$\{.*\}$', prop):
            flag = re.findall(r'^\$\{(.*)\}$', prop)[0]
            prop_flag = flag.split('.')
            return cp_ext.get(prop_flag[0], prop_flag[1])
        else:
            return prop

