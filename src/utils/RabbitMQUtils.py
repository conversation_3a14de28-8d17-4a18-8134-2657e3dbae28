# -*- coding: utf-8 -*- #
import json
import threading
import time
from typing import Optional, Dict, Union, Any

import pika

from src.controller import WeboLookBack, WechatLookBack
from src.entity import Agent
from src.log import Logger


class Helper:
    webo_get_data_event = threading.Event()
    wechat_get_data_event = threading.Event()
    webo_check_url_list = []
    wechat_check_url_list = []
    @classmethod
    def push_webo_lookback_result(cls, data):
        try:
            # 创建凭证，使用rabbitmq用户密码登录
            credentials = pika.PlainCredentials(Agent.Param.mq_username, Agent.Param.mq_password)
            # 新建连接到服务器ip
            connection = pika.BlockingConnection(pika.ConnectionParameters(host=Agent.Param.mq_host,
                                                                           port=int(Agent.Param.mq_port),
                                                                           credentials=credentials,
                                                                           heartbeat=0))
            # 创建频道
            channel = connection.channel()
            # 声明一个队列，用于接收消息，队列名字叫“消息队列1”
            channel.queue_declare(queue=Agent.Param.mq_result_queue_webo, durable=True)
            # 注意在rabbitmq中，消息想要发送给队列，必须经过交换(exchange)，初学可以使用空字符串交换(exchange='')，它允许我们精确的指定发送给哪个队列(routing_key=''),参数body值发送的数据
            channel.basic_publish(exchange=Agent.Param.mq_result_exchange,
                                  routing_key=Agent.Param.mq_webo_route_key,
                                  body=json.dumps(data, ensure_ascii=False),
                                  properties=pika.BasicProperties(delivery_mode=2, ))
            channel.close()
            connection.close()
        except Exception as e:
            Logger.Log.error("mq_push_error:" + e.__str__())

    @classmethod
    def push_task_test(cls, data):
        try:
            # 创建凭证，使用rabbitmq用户密码登录
            credentials = pika.PlainCredentials(Agent.Param.mq_username, Agent.Param.mq_password)
            # 新建连接到服务器ip
            connection = pika.BlockingConnection(pika.ConnectionParameters(host=Agent.Param.mq_host,
                                                                           port=int(Agent.Param.mq_port),
                                                                           credentials=credentials,
                                                                           heartbeat=0))
            # 创建频道
            channel = connection.channel()
            # 声明一个队列，用于接收消息，队列名字叫“消息队列1”
            channel.queue_declare(queue='queue_xhw_lookback_wechat', durable=True)
            # 注意在rabbitmq中，消息想要发送给队列，必须经过交换(exchange)，初学可以使用空字符串交换(exchange='')，它允许我们精确的指定发送给哪个队列(routing_key=''),参数body值发送的数据
            channel.basic_publish(exchange='direct_xhw_lookback',
                                  routing_key='xhw_lookback_wechat',
                                  body=json.dumps(data, ensure_ascii=False),
                                  properties=pika.BasicProperties(delivery_mode=2, ))
            channel.close()
            connection.close()
        except Exception as e:
            Logger.Log.error("mq_push_error:" + e.__str__())

    @classmethod
    def push_wechat_lookback_result(cls, data):
        try:
            # 创建凭证，使用rabbitmq用户密码登录
            credentials = pika.PlainCredentials(Agent.Param.mq_username, Agent.Param.mq_password)
            # 新建连接到服务器ip
            connection = pika.BlockingConnection(pika.ConnectionParameters(host=Agent.Param.mq_host,
                                                                           port=int(Agent.Param.mq_port),
                                                                           credentials=credentials,
                                                                           heartbeat=0))
            # 创建频道
            channel = connection.channel()
            # 声明一个队列，用于接收消息，队列名字叫“消息队列1”
            channel.queue_declare(queue=Agent.Param.mq_task_queue_wechat, durable=True)
            # 注意在rabbitmq中，消息想要发送给队列，必须经过交换(exchange)，初学可以使用空字符串交换(exchange='')，它允许我们精确的指定发送给哪个队列(routing_key=''),参数body值发送的数据
            channel.basic_publish(exchange=Agent.Param.mq_result_exchange,
                                  routing_key=Agent.Param.mq_wechat_route_key,
                                  body=json.dumps(data, ensure_ascii=False),
                                  properties=pika.BasicProperties(delivery_mode=2, ))
            channel.close()
            connection.close()
        except Exception as e:
            Logger.Log.error("mq_push_error:" + e.__str__())

    @classmethod
    def webo_task_reciver(cls):
        cls.webo_get_data_event.set()
        # 创建凭证，使用rabbitmq用户密码登录
        credentials = pika.PlainCredentials(Agent.Param.mq_username, Agent.Param.mq_password)
        # 新建连接到服务器ip
        connection = pika.BlockingConnection(pika.ConnectionParameters(host=Agent.Param.mq_host,
                                                                       port=int(Agent.Param.mq_port),
                                                                       credentials=credentials,
                                                                       heartbeat=0))
        channel = connection.channel()
        channel.basic_qos(prefetch_count=1)
        channel.basic_consume(queue=Agent.Param.mq_task_queue_webo,
                              on_message_callback=cls._webo_task_callback,
                              auto_ack=False)
        channel.start_consuming()

    @classmethod
    def _webo_task_callback(cls, ch, method, properties, body):
        try:
            Logger.Log.info('收到微博消息：' + str(body, encoding='utf-8'))
            msg = json.loads(str(body, encoding='utf-8'))
            temp_url_list = list(filter(lambda x: x['url'] == msg['url'], cls.webo_check_url_list))
            if len(temp_url_list) > 0:
                if temp_url_list[0]['retry_num'] == 5:
                    data = {
                        'taskSid': msg['taskSid'],
                        'itemSid': msg['itemSid'],
                        'publishTime': None,
                        'title': '',
                        'content': 'Invalid URL',
                        'imgs': []
                    }
                    cls.push_webo_lookback_result(data)
                    ch.basic_ack(delivery_tag=method.delivery_tag)
                    Logger.Log.warning('url无法访问：' + str(body, encoding='utf-8'))
                    return
            WeboLookBack.Fun.get_data(msg)
            ch.basic_ack(delivery_tag=method.delivery_tag)
        except Exception as e:
            msg = json.loads(str(body, encoding='utf-8'))
            temp_url_list = list(filter(lambda x: x['url'] == msg['url'], cls.webo_check_url_list))
            if len(temp_url_list) > 0:
                temp_url_list[0].update({'retry_num': temp_url_list[0]['retry_num'] + 1})
            else:
                check_data = {'retry_num': 1,
                              'taskSid': msg['taskSid'],
                              'itemSid': msg['itemSid'],
                              'url': msg['url']}
                cls.webo_check_url_list.append(check_data)
            ch.basic_reject(delivery_tag=method.delivery_tag)
            Logger.Log.error(e.__str__())
        # while True:
        #     if cls.webo_get_data_event.is_set():
        #         cls.webo_get_data_event.clear()
        #         try:
        #             msg = json.loads(str(body, encoding='utf-8'))
        #             WeboLookBack.Fun.get_data(msg)
        #             ch.basic_ack(delivery_tag=method.delivery_tag)
        #         except Exception as e:
        #             ch.basic_rejec(delivery_tag=method.delivery_tag)
        #             Logger.Log.error(e.__str__())
        #         finally:
        #             cls.webo_get_data_event.set()
        #             break
        #     else:
        #         time.sleep(1)


    @classmethod
    def wechat_task_reciver(cls):
        cls.wechat_get_data_event.set()
        # 创建凭证，使用rabbitmq用户密码登录
        credentials = pika.PlainCredentials(Agent.Param.mq_username, Agent.Param.mq_password)
        # 新建连接到服务器ip
        connection = pika.BlockingConnection(pika.ConnectionParameters(host=Agent.Param.mq_host,
                                                                       port=int(Agent.Param.mq_port),
                                                                       credentials=credentials,
                                                                       heartbeat=0))
        channel = connection.channel()
        channel.basic_qos(prefetch_count=1)
        channel.basic_consume(queue=Agent.Param.mq_task_queue_wechat,
                              on_message_callback=cls._wechat_task_callback,
                              auto_ack=False)

        channel.start_consuming()
    @classmethod
    def _wechat_task_callback(cls, ch, method, properties, body):
        try:
            Logger.Log.info('收到公众号消息：' + str(body, encoding='utf-8'))
            msg = json.loads(str(body, encoding='utf-8'))
            temp_url_list = list(filter(lambda x: x['url'] == msg['url'], cls.wechat_check_url_list))
            if len(temp_url_list) > 0:
                if temp_url_list[0]['retry_num'] == 5:
                    data = {
                        'taskSid': msg['taskSid'],
                        'itemSid': msg['itemSid'],
                        'publishTime': None,
                        'title': '',
                        'content': 'Invalid URL',
                        'imgs': []
                    }
                    cls.push_wechat_lookback_result(data)
                    ch.basic_ack(delivery_tag=method.delivery_tag)
                    Logger.Log.warning('url无法访问：' + str(body, encoding='utf-8'))
                    return
            WechatLookBack.Fun.get_data(msg)
            ch.basic_ack(delivery_tag=method.delivery_tag)
        except Exception as e:
            msg = json.loads(str(body, encoding='utf-8'))
            temp_url_list = list(filter(lambda x: x['url'] == msg['url'], cls.wechat_check_url_list))
            if len(temp_url_list) > 0:
                temp_url_list[0].update({'retry_num': temp_url_list[0]['retry_num'] + 1})
            else:
                check_data = {'retry_num': 1,
                              'taskSid': msg['taskSid'],
                              'itemSid': msg['itemSid'],
                              'url': msg['url']}
                cls.wechat_check_url_list.append(check_data)
            ch.basic_reject(delivery_tag=method.delivery_tag)
            Logger.Log.error(e.__str__())
        # while True:
        #     if cls.wechat_get_data_event.is_set():
        #         cls.webo_get_data_event.clear()
        #         try:
        #             msg = json.loads(str(body, encoding='utf-8'))
        #             WechatLookBack.Fun.get_data(msg)
        #             ch.basic_ack(delivery_tag=method.delivery_tag)
        #         except Exception as e:
        #             ch.basic_rejec(delivery_tag=method.delivery_tag)
        #         finally:
        #             cls.wechat_get_data_event.is_set()
        #             break
        #     else:
        #         time.sleep(1)
