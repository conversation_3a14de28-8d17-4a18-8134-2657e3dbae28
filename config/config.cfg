[service]
version: v1.0.1
env: test
port: 9901

[RabbitMQ]
host: ${RabbitMQ.host}
port: ${RabbitMQ.port}
username: ${RabbitMQ.username}
password: ${RabbitMQ.password}

task-exchange: ${RabbitMQ.task-exchange}
task-queue-webo: ${RabbitMQ.task-queue-webo}
task-queue-wechat: ${RabbitMQ.task-queue-wechat}

result-exchange: ${RabbitMQ.result-exchange}
result-queue-webo: ${RabbitMQ.result-queue-webo}
result-queue-wechat: ${RabbitMQ.result-queue-wechat}

webo-route-key: ${RabbitMQ.webo-route-key}
wechat-route-key: ${RabbitMQ.wechat-route-key}

