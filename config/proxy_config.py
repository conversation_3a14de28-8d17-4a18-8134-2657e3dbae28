# -*- coding: utf-8 -*-
"""
代理配置文件
请在此文件中配置您的代理服务器
"""

# 代理服务器列表
PROXY_LIST = [
    # 格式：'***************************:port' 或 'http://ip:port'
    # 示例：
    'http://127.0.0.1:7890',  # 本地代理示例
    'http://127.0.0.1:8080',  # 本地代理示例
    # 'http://proxy1.example.com:8080',
    # 'http://user:<EMAIL>:8080',
    # 'socks5://proxy3.example.com:1080',
]

# 代理轮换策略
PROXY_ROTATION_STRATEGY = 'round_robin'  # 'round_robin', 'random', 'failover'

# 代理超时设置
PROXY_TIMEOUT = 30

# 是否启用代理
ENABLE_PROXY = False

# 备用方案：使用免费代理池
FREE_PROXY_ENABLED = False
FREE_PROXY_URLS = [
    'https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all&ssl=all&anonymity=all',
    'https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt',
]

# 代理验证URL
PROXY_TEST_URL = 'http://httpbin.org/ip'

# 代理验证超时
PROXY_TEST_TIMEOUT = 10 