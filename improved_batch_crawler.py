#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import time
import random
import pandas as pd
from pathlib import Path
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(__file__))

from src.controller.WechatLookBackAdvanced import WechatLookBackAdvanced
from src.log.Logger import Log

class ImprovedBatchCrawler:
    def __init__(self):
        # 配置路径
        self.input_dir = r"/Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/xlsx_files"
        self.output_dir = r"/Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成"
        self.progress_file = "improved_crawl_progress.json"
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 初始化控制器
        self.controller = WechatLookBackAdvanced()
        
        # 初始化Logger
        Log.init(printflag=True, level='info')
        
        # 加载进度
        self.progress = self.load_progress()
        
    def load_progress(self):
        """加载进度文件"""
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            Log.error(f"加载进度文件失败: {str(e)}")
        
        return {
            'processed_files': [],
            'current_file': None,
            'current_row': 0
        }
    
    def save_progress(self):
        """保存进度"""
        try:
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress, f, ensure_ascii=False, indent=2)
        except Exception as e:
            Log.error(f"保存进度文件失败: {str(e)}")
    
    def get_unprocessed_files(self):
        """获取未处理的文件列表"""
        try:
            if not os.path.exists(self.input_dir):
                Log.error(f"输入目录不存在: {self.input_dir}")
                return []
            
            # 获取所有xlsx文件
            xlsx_files = []
            for file in os.listdir(self.input_dir):
                if file.endswith('.xlsx') or file.endswith('.xls'):
                    xlsx_files.append(file)
            
            # 过滤掉已完成的文件
            unprocessed_files = []
            for file in xlsx_files:
                output_file = os.path.join(self.output_dir, file)
                if not os.path.exists(output_file):
                    unprocessed_files.append(file)
                else:
                    Log.info(f"文件已存在，跳过: {file}")
            
            return unprocessed_files
            
        except Exception as e:
            Log.error(f"获取文件列表失败: {str(e)}")
            return []
    
    def improved_verification_handling(self, driver):
        """改进的验证页面处理"""
        try:
            # 等待页面加载
            time.sleep(3)
            
            # 检查是否已经是文章页面
            if self.is_article_page_improved(driver):
                Log.info("页面已经是文章页面，无需验证")
                return True
            
            # 查找验证按钮 - 使用更多选择器
            button_selectors = [
                '//button[contains(text(), "验证")]',
                '//a[contains(text(), "验证")]',
                '//div[contains(text(), "验证")]',
                '//span[contains(text(), "验证")]',
                '//button[contains(text(), "点击")]',
                '//a[contains(text(), "点击")]',
                '//div[contains(text(), "点击")]',
                '//span[contains(text(), "点击")]',
                '//button[contains(text(), "Verify")]',
                '//a[contains(text(), "Verify")]',
                '//div[contains(text(), "Verify")]',
                '//span[contains(text(), "Verify")]',
                '//button[contains(@class, "verify")]',
                '//a[contains(@class, "verify")]',
                '//div[contains(@class, "verify")]',
                '//span[contains(@class, "verify")]',
                '//button[contains(@class, "btn")]',
                '//a[contains(@class, "btn")]',
                '//div[contains(@class, "btn")]',
                '//span[contains(@class, "btn")]'
            ]
            
            verification_buttons = []
            for selector in button_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            verification_buttons.append(element)
                except:
                    continue
            
            if verification_buttons:
                Log.info(f"找到{len(verification_buttons)}个验证按钮")
                
                # 点击第一个可用的验证按钮
                verification_buttons[0].click()
                Log.info("已点击验证按钮")
                
                # 等待验证结果 - 减少等待时间
                max_wait_time = 15
                start_time = time.time()
                
                while time.time() - start_time < max_wait_time:
                    time.sleep(1)
                    
                    # 检查是否成功进入文章页面
                    if self.is_article_page_improved(driver):
                        Log.info("验证成功，已进入文章页面")
                        return True
                    
                    # 检查是否出现新的验证要求
                    try:
                        new_verification = driver.find_elements(By.XPATH, '//*[contains(text(), "滑块") or contains(text(), "滑动") or contains(text(), "slider")]')
                        if new_verification:
                            Log.warning("检测到滑块验证，跳过")
                            return False
                    except:
                        pass
                
                Log.warning(f"验证超时，等待了{max_wait_time}秒仍未成功")
                return False
            else:
                Log.warning("未找到可用的验证按钮")
                return False
                
        except Exception as e:
            Log.error(f"验证过程出错: {str(e)}")
            return False
    
    def is_article_page_improved(self, driver):
        """改进的文章页面检测"""
        try:
            # 检查多种可能的内容元素
            content_selectors = [
                '//div[@id="js_content"]',
                '//div[contains(@class, "rich_media_content")]',
                '//div[contains(@class, "content")]',
                '//article',
                '//div[contains(@class, "article")]',
                '//div[contains(@class, "post")]'
            ]
            
            for selector in content_selectors:
                try:
                    element = driver.find_element(By.XPATH, selector)
                    if element and element.text.strip():
                        Log.info(f"检测到文章内容元素: {selector}")
                        return True
                except:
                    continue
            
            # 检查URL
            current_url = driver.current_url
            if '/s?' in current_url and '__biz=' in current_url:
                Log.info("检测到文章URL")
                return True
            
            # 检查页面标题
            try:
                title = driver.title
                if title and len(title.strip()) > 5:
                    Log.info("检测到页面标题")
                    return True
            except:
                pass
            
            return False
            
        except Exception as e:
            Log.warning(f"检查文章页面时出错: {str(e)}")
            return False
    
    def process_single_url_improved(self, url, row_index, df):
        """改进的单URL处理"""
        try:
            Log.info(f"处理URL {row_index + 1}: {url}")
            
            # 构造消息
            msg = {
                'url': url,
                'taskSid': f'batch_task_{row_index}',
                'itemSid': f'batch_item_{row_index}',
                'title': '',
                'content': '',
                'publishTime': '',
                'imgs': ''
            }
            
            # 尝试获取数据
            result = self.controller.get_data_optimized(msg)
            
            if result:
                # 获取数据
                title = result.get('title', '')
                content = result.get('content', '')
                
                Log.info(f"提取到数据 - 标题长度: {len(title)}, 内容长度: {len(content)}")
                
                # 强制转换为字符串类型，确保数据类型一致
                title = str(title) if title else ''
                content = str(content) if content else ''
                
                # 更新DataFrame - 使用用户指定的列名
                df.at[row_index, '文章标题'] = title
                df.at[row_index, '文章内容'] = content
                
                # 强制刷新DataFrame，确保数据写入
                df = df.copy()
                
                # 等待一小段时间确保数据写入完成
                time.sleep(0.1)
                
                # 验证数据是否写入成功 - 多次验证确保数据真正写入
                verification_attempts = 0
                max_verification_attempts = 3
                data_written = False
                
                while verification_attempts < max_verification_attempts:
                    verification_attempts += 1
                    saved_title = df.at[row_index, '文章标题']
                    saved_content = df.at[row_index, '文章内容']
                    
                    # 检查数据是否真正写入
                    title_written = saved_title == title
                    content_written = saved_content == content
                    
                    if title_written and content_written:
                        Log.info(f"数据写入验证成功 (第{verification_attempts}次验证) - 标题: {len(str(saved_title))}, 内容: {len(str(saved_content))}")
                        data_written = True
                        break
                    else:
                        Log.warning(f"数据写入验证失败 (第{verification_attempts}次验证) - 标题匹配: {title_written}, 内容匹配: {content_written}")
                        # 重新写入数据
                        df.at[row_index, '文章标题'] = title
                        df.at[row_index, '文章内容'] = content
                        df = df.copy()
                        time.sleep(0.2)
                
                if data_written:
                    Log.info(f"✅ URL {row_index + 1} 处理成功")
                    return True
                else:
                    Log.error(f"❌ URL {row_index + 1} 数据写入失败")
                    return False
            else:
                Log.warning(f"❌ URL {row_index + 1} 处理失败")
                return False
                
        except Exception as e:
            Log.error(f"处理URL {row_index + 1} 时出错: {str(e)}")
            return False
    
    def process_file_improved(self, filename):
        """改进的文件处理"""
        try:
            input_file = os.path.join(self.input_dir, filename)
            temp_file = os.path.join(self.output_dir, f"temp_improved_{filename}")
            
            Log.info(f"开始处理文件: {filename}")
            
            # 检查是否有临时文件
            if os.path.exists(temp_file):
                Log.info(f"找到临时文件: {temp_file}")
                df = pd.read_excel(temp_file)
                Log.info(f"从临时文件加载数据: {temp_file}")
            else:
                df = pd.read_excel(input_file)
                # 添加必要的列（如果不存在）
                if '文章标题' not in df.columns:
                    df['文章标题'] = ''
                if '文章内容' not in df.columns:
                    df['文章内容'] = ''
            
            # 获取起始行
            start_row = self.progress.get('current_row', 0) if self.progress.get('current_file') == filename else 0
            
            if start_row > 0:
                Log.info(f"从上次中断的地方继续: 第 {start_row + 1} 行")
            
            # 处理每一行
            for i in range(start_row, len(df)):
                # 尝试不同的URL列名
                url = None
                for col_name in ['url', '文章地址', 'URL', '链接', 'link']:
                    if col_name in df.columns:
                        url = df.iloc[i].get(col_name, '')
                        if url and not pd.isna(url):
                            break
                
                if not url or pd.isna(url):
                    Log.info(f"跳过第 {i+1} 行：未找到有效URL")
                    continue
                
                # 处理URL
                success = self.process_single_url_improved(url, i, df)
                
                # 保存临时文件
                df.to_excel(temp_file, index=False)
                Log.info(f"已保存临时文件: {temp_file}")
                
                # 验证保存的文件是否包含数据
                try:
                    # 重新读取保存的文件进行验证
                    saved_df = pd.read_excel(temp_file)
                    current_row_data = saved_df.iloc[i]
                    saved_title = current_row_data.get('文章标题', '')
                    saved_content = current_row_data.get('文章内容', '')
                    
                    if saved_title and saved_content:
                        Log.info(f"文件保存验证成功 - 行 {i+1}: 标题长度 {len(str(saved_title))}, 内容长度 {len(str(saved_content))}")
                    else:
                        Log.warning(f"文件保存验证失败 - 行 {i+1}: 标题为空或内容为空")
                except Exception as e:
                    Log.error(f"文件保存验证出错: {str(e)}")
                
                # 更新进度
                self.progress['current_file'] = filename
                self.progress['current_row'] = i + 1
                self.save_progress()
                
                # 优化等待时间
                if success:
                    wait_time = random.uniform(2, 4)  # 成功时减少等待
                else:
                    wait_time = random.uniform(5, 8)  # 失败时适当等待
                
                Log.info(f"等待 {wait_time:.1f} 秒...")
                time.sleep(wait_time)
            
            # 保存最终文件
            output_file = os.path.join(self.output_dir, filename)
            df.to_excel(output_file, index=False)
            Log.info(f"✅ 文件处理完成: {output_file}")
            
            # 删除临时文件
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                    Log.info(f"已删除临时文件: {temp_file}")
                except Exception as e:
                    Log.warning(f"删除临时文件失败: {temp_file}, 错误: {str(e)}")
            
            # 更新进度
            self.progress['processed_files'].append(filename)
            self.progress['current_file'] = None
            self.progress['current_row'] = 0
            self.save_progress()
            
            return True
            
        except Exception as e:
            Log.error(f"处理文件 {filename} 时出错: {str(e)}")
            return False
    
    def run(self):
        """运行改进的批量爬取"""
        try:
            Log.info("=" * 50)
            Log.info("开始改进版批量爬取任务")
            Log.info(f"输入目录: {self.input_dir}")
            Log.info(f"输出目录: {self.output_dir}")
            Log.info("=" * 50)
            
            # 获取未处理的文件
            unprocessed_files = self.get_unprocessed_files()
            
            if not unprocessed_files:
                Log.info("没有需要处理的文件")
                return
            
            Log.info(f"找到 {len(unprocessed_files)} 个待处理文件")
            
            # 处理每个文件
            for i, filename in enumerate(unprocessed_files):
                Log.info(f"处理文件 {i + 1}/{len(unprocessed_files)}: {filename}")
                
                success = self.process_file_improved(filename)
                
                if success:
                    Log.info(f"✅ 文件 {filename} 处理成功")
                else:
                    Log.error(f"❌ 文件 {filename} 处理失败")
                
                # 文件间等待 - 减少等待时间
                if i < len(unprocessed_files) - 1:
                    wait_time = random.uniform(10, 20)
                    Log.info(f"等待 {wait_time:.1f} 秒后处理下一个文件...")
                    time.sleep(wait_time)
            
            Log.info("=" * 50)
            Log.info("🎉 改进版批量爬取任务完成！")
            Log.info("=" * 50)
            
        except Exception as e:
            Log.error(f"批量爬取过程中出现异常: {str(e)}")

def main():
    """主函数"""
    crawler = ImprovedBatchCrawler()
    crawler.run()

if __name__ == "__main__":
    main() 