# -*- coding: utf-8 -*-

# 🔥 重要：必须先初始化Logger才能看到控制台输出！
from src.log import Logger
Logger.Log.init(printflag=True, level='info')  # 启用控制台输出

from src.controller.WechatLookBackAdvancedUtil import WechatLookBackAdvancedUtil

def test_util_with_logger():
    """测试工具类并显示Logger输出"""
    
    print("🚀 测试WechatLookBackAdvancedUtil工具类（带Logger输出）")
    print("="*80)
    print("📋 Logger已正确初始化，您应该能看到详细的日志输出")
    print("="*80)
    
    # 测试URL
    url = "https://mp.weixin.qq.com/s?__biz=Mzk0OTYwMjk4Nw==&mid=2247485670&idx=1&sn=2d9b0a41233acb7b97dcd6d16f2408ec&chksm=c3549722f4231e342148686a0b4f4847e0706051cfc384731c9d7909f031c1e9a660b4104c5a#rd"
    
    print(f"\n📋 测试URL: {url}")
    print("-" * 70)
    
    try:
        # 现在您应该能看到详细的日志输出
        result = WechatLookBackAdvancedUtil.get_wechat_content(url)
        
        if result:
            print("\n✅ 测试成功!")
            print(f"  📝 标题: {result.get('title', '')[:50]}...")
            print(f"  📅 发布时间: {result.get('publishTime', '')}")
            print(f"  📄 内容长度: {len(result.get('content', ''))} 字符")
            print(f"  🖼️ 图片数量: {len(result.get('imgs', '').split(',')) if result.get('imgs') else 0}")
        else:
            print("❌ 测试失败")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🎯 Logger使用说明:")
    print("✅ 在使用WechatLookBackAdvancedUtil之前，必须先初始化Logger:")
    print("   from src.log import Logger")
    print("   Logger.Log.init(printflag=True, level='info')")
    print("✅ printflag=True 启用控制台输出")
    print("✅ level='info' 设置日志级别（debug/info/warning/error）")

if __name__ == "__main__":
    test_util_with_logger()
