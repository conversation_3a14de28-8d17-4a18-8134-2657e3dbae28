# -*- coding: utf-8 -*-
import requests
import time
import random
from bs4 import BeautifulSoup

def test_strategy_1(url):
    """策略1: 改进的微信浏览器模拟"""
    print("=== 策略1: 改进的微信浏览器模拟 ===")
    
    session = requests.Session()
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.28(0x18001c2f) NetType/WIFI Language/zh_CN',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
        'Referer': 'https://mp.weixin.qq.com/',
        'X-Requested-With': 'com.tencent.mm'
    }
    
    try:
        response = session.get(url, headers=headers, timeout=30)
        return analyze_response(response, "策略1")
    except Exception as e:
        print(f"策略1失败: {e}")
        return False

def test_strategy_2(url):
    """策略2: 先访问微信主页，再访问文章"""
    print("=== 策略2: 先访问微信主页，再访问文章 ===")
    
    session = requests.Session()
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.28(0x18001c2f) NetType/WIFI Language/zh_CN',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    }
    
    try:
        # 先访问微信主页
        print("先访问微信主页...")
        session.get('https://mp.weixin.qq.com/', headers=headers, timeout=30)
        time.sleep(2)
        
        # 再访问文章
        print("访问文章...")
        headers['Referer'] = 'https://mp.weixin.qq.com/'
        response = session.get(url, headers=headers, timeout=30)
        return analyze_response(response, "策略2")
    except Exception as e:
        print(f"策略2失败: {e}")
        return False

def test_strategy_3(url):
    """策略3: 使用PC版微信浏览器"""
    print("=== 策略3: 使用PC版微信浏览器 ===")
    
    session = requests.Session()
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 MicroMessenger/********',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0'
    }
    
    try:
        response = session.get(url, headers=headers, timeout=30)
        return analyze_response(response, "策略3")
    except Exception as e:
        print(f"策略3失败: {e}")
        return False

def test_strategy_4(url):
    """策略4: 尝试HTTPS版本的URL"""
    print("=== 策略4: 尝试HTTPS版本的URL ===")
    
    # 将http改为https
    https_url = url.replace('http://', 'https://')
    print(f"HTTPS URL: {https_url}")
    
    session = requests.Session()
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.28(0x18001c2f) NetType/WIFI Language/zh_CN',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Referer': 'https://mp.weixin.qq.com/'
    }
    
    try:
        response = session.get(https_url, headers=headers, timeout=30)
        return analyze_response(response, "策略4")
    except Exception as e:
        print(f"策略4失败: {e}")
        return False

def analyze_response(response, strategy_name):
    """分析响应内容"""
    print(f"{strategy_name} - 响应状态码: {response.status_code}")
    print(f"{strategy_name} - 响应内容长度: {len(response.text)}")
    
    # 检查是否是验证页面
    if 'secitptpage/verify' in response.text:
        print(f"{strategy_name} - ❌ 返回验证页面")
        return False
    
    if 'weui-msg' in response.text and len(response.text) < 20000:
        print(f"{strategy_name} - ❌ 返回消息页面")
        return False
    
    # 解析HTML
    soup = BeautifulSoup(response.text, "html.parser")
    
    # 检查标题
    title_element = soup.find(attrs={'property': 'og:title'})
    if title_element:
        title = title_element.attrs.get('content', '')
        print(f"{strategy_name} - ✅ 找到标题: {title}")
        
        # 检查内容
        content_element = soup.find('div', {'id': 'js_content'})
        if content_element:
            content = content_element.get_text(separator='\n')
            print(f"{strategy_name} - ✅ 找到内容，长度: {len(content)}")
            print(f"{strategy_name} - 内容前100字符: {content[:100]}")
            return True
        else:
            print(f"{strategy_name} - ❌ 未找到内容元素")
            return False
    else:
        print(f"{strategy_name} - ❌ 未找到标题元素")
        return False

def main():
    url = "http://mp.weixin.qq.com/s?__biz=MzIzOTEwNTI1NQ==&mid=2247507915&idx=1&sn=79351567dbbd9b2ac184cdc6b0b7c79b&scene=0"
    
    print("开始测试多种访问策略...")
    print(f"测试URL: {url}")
    print("="*80)
    
    strategies = [
        test_strategy_1,
        test_strategy_2, 
        test_strategy_3,
        test_strategy_4
    ]
    
    for i, strategy in enumerate(strategies, 1):
        print(f"\n第{i}种策略:")
        success = strategy(url)
        if success:
            print(f"✅ 策略{i}成功！")
            break
        else:
            print(f"❌ 策略{i}失败")
        
        # 策略间等待
        if i < len(strategies):
            wait_time = random.randint(3, 8)
            print(f"等待{wait_time}秒后尝试下一个策略...")
            time.sleep(wait_time)
    
    print("\n测试完成")

if __name__ == "__main__":
    main()
