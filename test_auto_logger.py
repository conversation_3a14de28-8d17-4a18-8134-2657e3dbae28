# -*- coding: utf-8 -*-

# 🎯 测试自动初始化Logger功能
# 注意：这次我们不手动初始化Logger，让工具类自动初始化

from src.controller.WechatLookBackAdvancedUtil import WechatLookBackAdvancedUtil

def test_auto_logger():
    """测试工具类自动初始化Logger功能"""
    
    print("🚀 测试WechatLookBackAdvancedUtil自动初始化Logger功能")
    print("="*80)
    print("📋 这次我们不手动初始化Logger，让工具类自动处理")
    print("📋 您应该看到'Logger已自动初始化'的消息，然后是详细日志")
    print("="*80)
    
    # 测试URL
    url = "https://mp.weixin.qq.com/s?__biz=Mzk0OTYwMjk4Nw==&mid=2247485670&idx=1&sn=2d9b0a41233acb7b97dcd6d16f2408ec&chksm=c3549722f4231e342148686a0b4f4847e0706051cfc384731c9d7909f031c1e9a660b4104c5a#rd"
    
    print(f"\n📋 测试URL: {url}")
    print("-" * 70)
    
    try:
        # 工具类会自动初始化Logger
        result = WechatLookBackAdvancedUtil.get_wechat_content(url, auto_init_logger=True)
        
        if result:
            print("\n✅ 测试成功!")
            print(f"  📝 标题: {result.get('title', '')[:50]}...")
            print(f"  📅 发布时间: {result.get('publishTime', '')}")
            print(f"  📄 内容长度: {len(result.get('content', ''))} 字符")
            print(f"  🖼️ 图片数量: {len(result.get('imgs', '').split(',')) if result.get('imgs') else 0}")
        else:
            print("❌ 测试失败")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🎯 自动Logger功能说明:")
    print("✅ 工具类现在支持自动初始化Logger")
    print("✅ 如果Logger未初始化，工具类会自动设置控制台输出")
    print("✅ 您可以通过 auto_init_logger=False 禁用自动初始化")
    print("✅ 推荐在项目开始时手动初始化Logger以获得更好的控制")

if __name__ == "__main__":
    test_auto_logger()
