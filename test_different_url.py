# -*- coding: utf-8 -*-
import requests
import time
from bs4 import BeautifulSoup

def test_wechat_url(url, description):
    """测试微信公众号URL"""
    print(f"\n=== {description} ===")
    print(f"URL: {url}")
    
    session = requests.Session()
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.28(0x18001c2f) NetType/WIFI Language/zh_CN',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Referer': 'https://mp.weixin.qq.com/'
    }
    
    try:
        response = session.get(url, headers=headers, timeout=30)
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容长度: {len(response.text)}")
        
        # 检查是否是验证页面
        if 'secitptpage/verify' in response.text:
            print("❌ 返回验证页面")
            return False
        
        if 'weui-msg' in response.text and len(response.text) < 20000:
            print("❌ 返回消息页面")
            return False
        
        # 解析HTML
        soup = BeautifulSoup(response.text, "html.parser")
        
        # 检查标题
        title_element = soup.find(attrs={'property': 'og:title'})
        if title_element:
            title = title_element.attrs.get('content', '')
            print(f"✅ 找到标题: {title}")
            
            # 检查内容
            content_element = soup.find('div', {'id': 'js_content'})
            if content_element:
                content = content_element.get_text(separator='\n')
                print(f"✅ 找到内容，长度: {len(content)}")
                print(f"内容前200字符: {content[:200]}")
                return True
            else:
                print("❌ 未找到内容元素")
                return False
        else:
            print("❌ 未找到标题元素")
            # 保存HTML用于调试
            with open(f'debug_{description.replace(" ", "_")}.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"HTML已保存到 debug_{description.replace(' ', '_')}.html")
            return False
            
    except Exception as e:
        print(f"请求失败: {e}")
        return False

def main():
    # 测试不同的URL
    test_urls = [
        # 原始测试URL
        ("http://mp.weixin.qq.com/s?__biz=MzIzOTEwNTI1NQ==&mid=2247507915&idx=1&sn=79351567dbbd9b2ac184cdc6b0b7c79b&scene=0", "原始测试URL"),
        
        # 注释中的其他URL
        ("https://mp.weixin.qq.com/s/zfCnN0Un8oujCrMA1lOkpg", "简化URL 1"),
        
        ("https://mp.weixin.qq.com/s?__biz=Mzk0OTYwMjk4Nw==&mid=2247485670&idx=1&sn=2d9b0a41233acb7b97dcd6d16f2408ec&chksm=c3549722f4231e342148686a0b4f4847e0706051cfc384731c9d7909f031c1e9a660b4104c5a#rd", "完整URL"),
        
        # 尝试一个更新的URL格式
        ("https://mp.weixin.qq.com/s?__biz=MzIzOTEwNTI1NQ==&mid=2247507915&idx=1&sn=79351567dbbd9b2ac184cdc6b0b7c79b", "去掉scene参数"),
    ]
    
    print("开始测试不同的微信公众号URL...")
    print("="*80)
    
    success_count = 0
    for url, description in test_urls:
        success = test_wechat_url(url, description)
        if success:
            success_count += 1
            print(f"✅ {description} 成功！")
        else:
            print(f"❌ {description} 失败")
        
        # URL间等待
        time.sleep(3)
    
    print(f"\n测试完成，成功: {success_count}/{len(test_urls)}")

if __name__ == "__main__":
    main()
