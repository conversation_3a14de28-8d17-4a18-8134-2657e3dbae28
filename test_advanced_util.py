# -*- coding: utf-8 -*-
from src.log import Logger
from src.controller import WechatLookBack
from src.controller.WechatLookBackAdvancedUtil import WechatLookBackAdvancedUtil

# 初始化日志系统
Logger.Log.init(printflag=True, level='info')

def test_advanced_util():
    """测试新的WechatLookBackAdvancedUtil工具类"""
    
    print("🚀 测试WechatLookBackAdvancedUtil工具类")
    print("="*80)
    
    # 测试URL
    test_urls = [
        "https://mp.weixin.qq.com/s?__biz=Mzk0OTYwMjk4Nw==&mid=2247485670&idx=1&sn=2d9b0a41233acb7b97dcd6d16f2408ec&chksm=c3549722f4231e342148686a0b4f4847e0706051cfc384731c9d7909f031c1e9a660b4104c5a#rd",
        "https://mp.weixin.qq.com/s?__biz=MzI0MTgwOTg4Mg==&mid=2247483975&idx=1&sn=95074fa9035aa0e285ed917834646d5f&scene=0"
    ]
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n📋 测试{i}: {url}")
        print("-" * 70)
        
        try:
            # 直接测试工具类
            print("🔧 直接测试WechatLookBackAdvancedUtil.get_wechat_content():")
            result = WechatLookBackAdvancedUtil.get_wechat_content(url)
            
            if result:
                print("✅ 工具类测试成功!")
                print(f"  📝 标题: {result.get('title', '')[:50]}...")
                print(f"  📅 发布时间: {result.get('publishTime', '')}")
                print(f"  📄 内容长度: {len(result.get('content', ''))} 字符")
                print(f"  🖼️ 图片数量: {len(result.get('imgs', '').split(',')) if result.get('imgs') else 0}")
                
                # 测试通过WechatLookBack调用
                print("\n🔧 测试通过WechatLookBack.Fun.get_data()调用:")
                msg = {
                    "taskSid": f"test_util_{i}",
                    "itemSid": f"test_util_{i}",
                    "url": url
                }
                
                wechat_result = WechatLookBack.Fun.get_data(msg)
                if wechat_result:
                    print("✅ WechatLookBack调用成功!")
                    print(f"  📝 标题: {wechat_result.get('title', '')[:50]}...")
                    print(f"  📅 发布时间: {wechat_result.get('publishTime', '')}")
                    print(f"  📄 内容长度: {len(wechat_result.get('content', ''))} 字符")
                    print(f"  🖼️ 图片: {wechat_result.get('imgs', '')[:100]}...")
                else:
                    print("❌ WechatLookBack调用失败")
            else:
                print("❌ 工具类测试失败")
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")
        
        print("-" * 70)
    
    print("\n🎯 测试总结:")
    print("✅ WechatLookBackAdvancedUtil工具类已成功封装")
    print("✅ 提供简洁的get_wechat_content(url)接口")
    print("✅ 返回标准的{title, content, publishTime, imgs}字段")
    print("✅ WechatLookBack已集成工具类，保持原有接口")
    print("✅ 双重策略：高级工具类 + 原始方法备用")
    print("✅ 完整的错误处理和日志记录")

if __name__ == "__main__":
    test_advanced_util()
