# -*- coding: utf-8 -*-
from src.log import Logger

def test_logger():
    """测试Logger控制台输出"""
    
    print("🧪 测试Logger控制台输出...")
    print("="*50)
    
    # 测试不同的初始化方式
    test_cases = [
        {'printflag': True, 'level': 'debug', 'name': 'DEBUG级别'},
        {'printflag': True, 'level': 'info', 'name': 'INFO级别'},
        {'printflag': True, 'level': 'warning', 'name': 'WARNING级别'},
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📋 测试{i}: {case['name']}")
        print("-" * 30)
        
        # 重新初始化Logger
        Logger.Log.logger.handlers.clear()  # 清除之前的handlers
        Logger.Log.init(printflag=case['printflag'], level=case['level'])
        
        print(f"Logger级别设置为: {case['level']}")
        print("应该看到以下日志输出:")
        
        # 测试不同级别的日志
        Logger.Log.debug("🐛 这是DEBUG级别的日志")
        Logger.Log.info("ℹ️ 这是INFO级别的日志")
        Logger.Log.warning("⚠️ 这是WARNING级别的日志")
        Logger.Log.error("❌ 这是ERROR级别的日志")
        
        print("-" * 30)
    
    print("\n🎯 如果您看到了上面的日志输出，说明Logger工作正常！")
    print("如果没有看到，可能的原因：")
    print("1. Logger没有正确初始化")
    print("2. 日志级别设置过高")
    print("3. printflag参数为False")

if __name__ == "__main__":
    test_logger()
