# -*- coding: utf-8 -*-
import requests
from bs4 import BeautifulSoup

def analyze_wechat_response(url):
    """分析微信公众号响应的HTML内容"""
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.181 Mobile Safari/537.36 MicroMessenger/8.0.16.2040(0x28001056) Process/tools WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0'
    }
    
    try:
        response = requests.get(url, timeout=30, headers=headers)
        response.raise_for_status()
        response.encoding = response.apparent_encoding
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容长度: {len(response.text)}")
        
        # 保存完整HTML到文件
        with open('wechat_response.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("完整HTML已保存到 wechat_response.html")
        
        # 解析HTML
        soup = BeautifulSoup(response.text, "html.parser")
        
        print("\n=== HTML结构分析 ===")
        
        # 查找所有meta标签
        print("\n所有meta标签:")
        meta_tags = soup.find_all('meta')
        for meta in meta_tags:
            attrs = dict(meta.attrs)
            print(f"  {attrs}")
        
        # 查找title标签
        print("\ntitle标签:")
        title_tag = soup.find('title')
        if title_tag:
            print(f"  {title_tag.text}")
        else:
            print("  未找到title标签")
        
        # 查找og:title
        print("\nog:title标签:")
        og_title = soup.find(attrs={'property': 'og:title'})
        if og_title:
            print(f"  {og_title.attrs}")
        else:
            print("  未找到og:title标签")
        
        # 查找可能的标题元素
        print("\n可能的标题元素:")
        possible_titles = soup.find_all(['h1', 'h2', 'h3'])
        for title in possible_titles:
            print(f"  {title.name}: {title.text.strip()}")
        
        # 查找js_content
        print("\njs_content元素:")
        js_content = soup.find('div', {'id': 'js_content'})
        if js_content:
            print(f"  找到js_content，内容长度: {len(js_content.get_text())}")
        else:
            print("  未找到js_content元素")
        
        # 查找可能的内容容器
        print("\n可能的内容容器:")
        content_divs = soup.find_all('div', class_=lambda x: x and ('content' in x.lower() or 'article' in x.lower()))
        for div in content_divs:
            classes = div.get('class', [])
            print(f"  div.{'.'.join(classes)}: {len(div.get_text())} 字符")
        
        # 检查是否有验证相关内容
        print("\n验证相关检查:")
        verification_keywords = ['验证', '完成验证后即可继续访问', '当前环境异常', 'weui-msg']
        for keyword in verification_keywords:
            if keyword in response.text:
                print(f"  ❌ 发现关键词: {keyword}")
            else:
                print(f"  ✅ 未发现关键词: {keyword}")
        
        # 查找script标签中的数据
        print("\nscript标签分析:")
        scripts = soup.find_all('script')
        for i, script in enumerate(scripts):
            if script.string and len(script.string) > 50:
                content = script.string[:100].replace('\n', ' ')
                print(f"  script[{i}]: {content}...")
                
                # 查找createTime
                if 'createTime' in script.string:
                    print(f"    ✅ 发现createTime")
        
        return True
        
    except Exception as e:
        print(f"分析失败: {e}")
        return False

if __name__ == "__main__":
    url = "http://mp.weixin.qq.com/s?__biz=MzIzOTEwNTI1NQ==&mid=2247507915&idx=1&sn=79351567dbbd9b2ac184cdc6b0b7c79b&scene=0"
    analyze_wechat_response(url)
