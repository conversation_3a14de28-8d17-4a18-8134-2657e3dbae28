# -*- coding: utf-8 -*-

# 初始化Logger
from src.log import Logger
Logger.Log.init(printflag=True, level='info')

from src.controller.WechatLookBackAdvancedUtil import WechatLookBackAdvancedUtil

def test_clean_logs():
    """测试优化后的日志输出（减少不必要的错误信息）"""
    
    print("🚀 测试优化后的日志输出")
    print("="*80)
    print("📋 优化内容：")
    print("✅ 静默处理正常的NoSuchElementException")
    print("✅ 减少不必要的错误日志输出")
    print("✅ 保留重要的信息和真正的错误")
    print("="*80)
    
    # 测试URL
    url = "https://mp.weixin.qq.com/s?__biz=Mzk0OTYwMjk4Nw==&mid=2247485670&idx=1&sn=2d9b0a41233acb7b97dcd6d16f2408ec&chksm=c3549722f4231e342148686a0b4f4847e0706051cfc384731c9d7909f031c1e9a660b4104c5a#rd"
    
    print(f"\n📋 测试URL: {url}")
    print("-" * 70)
    print("现在您应该看到更清洁的日志输出，没有不必要的错误信息：")
    print("-" * 70)
    
    try:
        result = WechatLookBackAdvancedUtil.get_wechat_content(url)
        
        if result:
            print("\n✅ 测试成功!")
            print(f"  📝 标题: {result.get('title', '')[:50]}...")
            print(f"  📅 发布时间: {result.get('publishTime', '')}")
            print(f"  📄 内容长度: {len(result.get('content', ''))} 字符")
            print(f"  🖼️ 图片数量: {len(result.get('imgs', '').split(',')) if result.get('imgs') else 0}")
        else:
            print("❌ 测试失败")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🎯 日志优化说明:")
    print("✅ NoSuchElementException 现在被静默处理")
    print("✅ 只有真正的错误才会显示详细信息")
    print("✅ 保留了所有重要的操作日志")
    print("✅ 页面检测流程更加清洁")

if __name__ == "__main__":
    test_clean_logs()
