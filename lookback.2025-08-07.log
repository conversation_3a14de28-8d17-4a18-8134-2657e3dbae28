2025-08-07 19:50:36,502 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 19:50:36,502 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始改进版批量爬取任务
2025-08-07 19:50:36,502 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输入目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/xlsx_files
2025-08-07 19:50:36,502 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输出目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成
2025-08-07 19:50:36,502 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 19:50:36,502 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到 2 个待处理文件
2025-08-07 19:50:36,502 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理文件 1/2: XH-175327274434416.xlsx
2025-08-07 19:50:36,502 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理文件: XH-175327274434416.xlsx
2025-08-07 19:50:36,614 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从上次中断的地方继续: 第 429 行
2025-08-07 19:50:36,614 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 429: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247486572&idx=1&sn=08720de8b058767fc33ad60767f224d1&scene=0
2025-08-07 19:50:36,614 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247486572&idx=1&sn=08720de8b058767fc33ad60767f224d1&scene=0
2025-08-07 19:50:36,615 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败: Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-08-07 19:50:36,615 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败
2025-08-07 19:50:36,615 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: ❌ URL 429 处理失败
2025-08-07 19:50:36,667 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 19:50:36,704 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 429: 标题长度 3, 内容长度 3
2025-08-07 19:50:36,705 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 7.0 秒...
2025-08-07 19:50:43,681 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 430: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247484330&idx=1&sn=b11af829ab8624f89cce03a52f0072e6&scene=0
2025-08-07 19:50:43,682 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247484330&idx=1&sn=b11af829ab8624f89cce03a52f0072e6&scene=0
2025-08-07 19:50:43,684 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败: Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-08-07 19:50:43,684 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败
2025-08-07 19:50:43,684 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: ❌ URL 430 处理失败
2025-08-07 19:50:43,742 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 19:50:43,780 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 430: 标题长度 3, 内容长度 3
2025-08-07 19:50:43,781 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 7.1 秒...
2025-08-07 19:52:57,100 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 19:52:57,100 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始改进版批量爬取任务
2025-08-07 19:52:57,100 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输入目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/xlsx_files
2025-08-07 19:52:57,100 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输出目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成
2025-08-07 19:52:57,100 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 19:52:57,100 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到 2 个待处理文件
2025-08-07 19:52:57,100 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理文件 1/2: XH-175327274434416.xlsx
2025-08-07 19:52:57,100 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理文件: XH-175327274434416.xlsx
2025-08-07 19:52:57,100 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 19:52:57,184 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从临时文件加载数据: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 19:52:57,184 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从上次中断的地方继续: 第 431 行
2025-08-07 19:52:57,184 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 431: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247490706&idx=1&sn=c2d06867f1c0a5eae92279b7b5dcf20b&scene=0
2025-08-07 19:52:57,184 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247490706&idx=1&sn=c2d06867f1c0a5eae92279b7b5dcf20b&scene=0
2025-08-07 19:52:57,184 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败: Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-08-07 19:52:57,184 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败
2025-08-07 19:52:57,184 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: ❌ URL 431 处理失败
2025-08-07 19:52:57,234 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 19:52:57,270 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 431: 标题长度 3, 内容长度 3
2025-08-07 19:52:57,271 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 7.1 秒...
2025-08-07 19:53:04,418 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 432: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247489426&idx=1&sn=c9a1624d34fb57b11da96dc9a1970944&scene=0
2025-08-07 19:53:04,419 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247489426&idx=1&sn=c9a1624d34fb57b11da96dc9a1970944&scene=0
2025-08-07 19:53:04,420 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败: Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-08-07 19:53:04,421 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败
2025-08-07 19:53:04,421 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: ❌ URL 432 处理失败
2025-08-07 19:53:04,470 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 19:53:04,509 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 432: 标题长度 3, 内容长度 3
2025-08-07 19:53:04,510 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 6.0 秒...
2025-08-07 19:54:01,647 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 19:54:01,647 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始改进版批量爬取任务
2025-08-07 19:54:01,647 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输入目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/xlsx_files
2025-08-07 19:54:01,647 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输出目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成
2025-08-07 19:54:01,647 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 19:54:01,647 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到 2 个待处理文件
2025-08-07 19:54:01,647 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理文件 1/2: XH-175327274434416.xlsx
2025-08-07 19:54:01,647 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理文件: XH-175327274434416.xlsx
2025-08-07 19:54:01,647 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 19:54:01,731 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从临时文件加载数据: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 19:54:01,731 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从上次中断的地方继续: 第 433 行
2025-08-07 19:54:01,731 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 433: http://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247493843&idx=1&sn=1f6c1a303d89b93ab624158e49fd8dd5&scene=0
2025-08-07 19:54:01,731 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: http://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247493843&idx=1&sn=1f6c1a303d89b93ab624158e49fd8dd5&scene=0
2025-08-07 19:54:01,731 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败: Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-08-07 19:54:01,731 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败
2025-08-07 19:54:01,731 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: ❌ URL 433 处理失败
2025-08-07 19:54:01,781 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 19:54:01,818 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 433: 标题长度 3, 内容长度 3
2025-08-07 19:54:01,819 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 7.6 秒...
2025-08-07 19:54:36,260 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 19:54:36,260 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始改进版批量爬取任务
2025-08-07 19:54:36,260 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输入目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/xlsx_files
2025-08-07 19:54:36,260 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输出目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成
2025-08-07 19:54:36,260 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 19:54:36,260 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到 2 个待处理文件
2025-08-07 19:54:36,260 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理文件 1/2: XH-175327274434416.xlsx
2025-08-07 19:54:36,261 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理文件: XH-175327274434416.xlsx
2025-08-07 19:54:36,261 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 19:54:36,508 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从临时文件加载数据: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 19:54:36,509 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从上次中断的地方继续: 第 434 行
2025-08-07 19:54:36,509 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 434: https://mp.weixin.qq.com/s/T8mEG-f4I3IAoX6BILb3dg
2025-08-07 19:54:36,509 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s/T8mEG-f4I3IAoX6BILb3dg
2025-08-07 19:54:55,925 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 数据获取过程中出现异常: 'NoSuchDriverException' object is not callable
2025-08-07 19:55:02,029 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: ❌ URL 434 处理失败
2025-08-07 19:55:02,140 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 19:55:02,252 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 434: 标题长度 3, 内容长度 3
2025-08-07 19:55:02,253 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 7.2 秒...
2025-08-07 19:55:09,497 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 435: https://mp.weixin.qq.com/s/_gnCyLCeWCmZl5Cu8GCvpg
2025-08-07 19:55:09,497 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s/_gnCyLCeWCmZl5Cu8GCvpg
2025-08-07 19:55:15,564 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 数据获取过程中出现异常: 'KeyboardInterrupt' object is not callable
2025-08-07 19:55:15,564 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: ❌ URL 435 处理失败
2025-08-07 19:55:15,635 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:01:15,344 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 20:01:15,344 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始改进版批量爬取任务
2025-08-07 20:01:15,344 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输入目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/xlsx_files
2025-08-07 20:01:15,344 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输出目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成
2025-08-07 20:01:15,344 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 20:01:15,344 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到 2 个待处理文件
2025-08-07 20:01:15,344 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理文件 1/2: XH-175327274434416.xlsx
2025-08-07 20:01:15,344 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理文件: XH-175327274434416.xlsx
2025-08-07 20:01:15,344 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:01:15,448 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从临时文件加载数据: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:01:15,448 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从上次中断的地方继续: 第 435 行
2025-08-07 20:01:15,448 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 435: https://mp.weixin.qq.com/s/_gnCyLCeWCmZl5Cu8GCvpg
2025-08-07 20:01:15,448 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s/_gnCyLCeWCmZl5Cu8GCvpg
2025-08-07 20:01:15,449 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败: Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-08-07 20:01:15,449 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败
2025-08-07 20:01:15,449 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: ❌ URL 435 处理失败
2025-08-07 20:01:15,499 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:01:15,535 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 435: 标题长度 3, 内容长度 3
2025-08-07 20:01:15,535 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 7.5 秒...
2025-08-07 20:01:23,061 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 436: http://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247494191&idx=1&sn=3ced4bb97c6ece83c1bc62f36ebf839f&chksm=c019670df76eee1be3447596c9e73137ac530a600c645f70cb1c66a16e2321118dff540c3470#rd
2025-08-07 20:01:23,062 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: http://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247494191&idx=1&sn=3ced4bb97c6ece83c1bc62f36ebf839f&chksm=c019670df76eee1be3447596c9e73137ac530a600c645f70cb1c66a16e2321118dff540c3470#rd
2025-08-07 20:01:23,062 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败: Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-08-07 20:01:23,062 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败
2025-08-07 20:01:23,062 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: ❌ URL 436 处理失败
2025-08-07 20:01:23,109 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:01:24,776 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 20:01:24,776 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始改进版批量爬取任务
2025-08-07 20:01:24,776 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输入目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/xlsx_files
2025-08-07 20:01:24,776 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输出目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成
2025-08-07 20:01:24,776 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 20:01:24,777 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到 2 个待处理文件
2025-08-07 20:01:24,777 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理文件 1/2: XH-175327274434416.xlsx
2025-08-07 20:01:24,777 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理文件: XH-175327274434416.xlsx
2025-08-07 20:01:24,777 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:01:24,894 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从临时文件加载数据: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:01:24,895 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从上次中断的地方继续: 第 436 行
2025-08-07 20:01:24,895 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 436: http://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247494191&idx=1&sn=3ced4bb97c6ece83c1bc62f36ebf839f&chksm=c019670df76eee1be3447596c9e73137ac530a600c645f70cb1c66a16e2321118dff540c3470#rd
2025-08-07 20:01:24,895 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: http://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247494191&idx=1&sn=3ced4bb97c6ece83c1bc62f36ebf839f&chksm=c019670df76eee1be3447596c9e73137ac530a600c645f70cb1c66a16e2321118dff540c3470#rd
2025-08-07 20:01:24,895 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败: Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-08-07 20:01:24,895 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败
2025-08-07 20:01:24,895 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: ❌ URL 436 处理失败
2025-08-07 20:01:24,950 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:01:25,018 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 436: 标题长度 3, 内容长度 3
2025-08-07 20:01:25,018 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 6.4 秒...
2025-08-07 20:01:42,274 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 20:01:42,274 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始改进版批量爬取任务
2025-08-07 20:01:42,275 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输入目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/xlsx_files
2025-08-07 20:01:42,275 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输出目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成
2025-08-07 20:01:42,275 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 20:01:42,275 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到 2 个待处理文件
2025-08-07 20:01:42,275 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理文件 1/2: XH-175327274434416.xlsx
2025-08-07 20:01:42,275 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理文件: XH-175327274434416.xlsx
2025-08-07 20:01:42,275 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:01:42,385 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从临时文件加载数据: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:01:42,385 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从上次中断的地方继续: 第 437 行
2025-08-07 20:01:42,385 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 437: https://mp.weixin.qq.com/s/OJ9SDoQwqWjrxqCzcSa7Fw
2025-08-07 20:01:42,386 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s/OJ9SDoQwqWjrxqCzcSa7Fw
2025-08-07 20:01:42,386 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败: Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-08-07 20:01:42,386 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败
2025-08-07 20:01:42,386 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: ❌ URL 437 处理失败
2025-08-07 20:01:42,440 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:01:42,476 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 437: 标题长度 3, 内容长度 3
2025-08-07 20:01:42,477 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 7.0 秒...
2025-08-07 20:02:21,467 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 20:02:21,468 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始改进版批量爬取任务
2025-08-07 20:02:21,468 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输入目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/xlsx_files
2025-08-07 20:02:21,468 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输出目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成
2025-08-07 20:02:21,468 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 20:02:21,468 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到 2 个待处理文件
2025-08-07 20:02:21,468 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理文件 1/2: XH-175327274434416.xlsx
2025-08-07 20:02:21,468 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理文件: XH-175327274434416.xlsx
2025-08-07 20:02:21,468 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:02:21,721 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从临时文件加载数据: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:02:21,721 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从上次中断的地方继续: 第 438 行
2025-08-07 20:02:21,722 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 438: http://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247492074&idx=1&sn=ba9bdbb0e6b9f557ff9b3d39dd6b6474&scene=0
2025-08-07 20:02:21,722 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: http://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247492074&idx=1&sn=ba9bdbb0e6b9f557ff9b3d39dd6b6474&scene=0
2025-08-07 20:02:33,928 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 数据获取过程中出现异常: 'KeyboardInterrupt' object is not callable
2025-08-07 20:02:33,928 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: ❌ URL 438 处理失败
2025-08-07 20:02:34,014 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:02:34,080 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 438: 标题长度 3, 内容长度 3
2025-08-07 20:02:34,081 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 5.6 秒...
2025-08-07 20:03:19,632 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 20:03:19,633 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始改进版批量爬取任务
2025-08-07 20:03:19,633 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输入目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/xlsx_files
2025-08-07 20:03:19,633 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输出目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成
2025-08-07 20:03:19,633 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 20:03:19,633 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到 2 个待处理文件
2025-08-07 20:03:19,633 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理文件 1/2: XH-175327274434416.xlsx
2025-08-07 20:03:19,633 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理文件: XH-175327274434416.xlsx
2025-08-07 20:03:19,633 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:03:19,879 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从临时文件加载数据: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:03:19,879 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从上次中断的地方继续: 第 439 行
2025-08-07 20:03:19,880 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 439: http://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247494236&idx=1&sn=9d9bb9752a04df2e5b67feb36d6a2697&chksm=c10a6ef021bcf1c2e224d482171a15d5c9cb6329d3651a9ccd7ed0ca3953324c0615eb091356#rd
2025-08-07 20:03:19,880 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: http://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247494236&idx=1&sn=9d9bb9752a04df2e5b67feb36d6a2697&chksm=c10a6ef021bcf1c2e224d482171a15d5c9cb6329d3651a9ccd7ed0ca3953324c0615eb091356#rd
2025-08-07 20:03:19,880 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败: Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-08-07 20:03:19,880 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败
2025-08-07 20:03:19,880 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: ❌ URL 439 处理失败
2025-08-07 20:03:19,974 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:03:20,090 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 439: 标题长度 3, 内容长度 3
2025-08-07 20:03:20,091 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 5.2 秒...
2025-08-07 20:03:25,259 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 440: https://mp.weixin.qq.com/s/vEblTPOY95xhhtaOOnkdAA
2025-08-07 20:03:25,259 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s/vEblTPOY95xhhtaOOnkdAA
2025-08-07 20:03:25,259 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败: Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-08-07 20:03:25,259 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败
2025-08-07 20:03:25,259 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: ❌ URL 440 处理失败
2025-08-07 20:03:25,352 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:03:25,475 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 440: 标题长度 3, 内容长度 3
2025-08-07 20:03:25,475 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 6.7 秒...
2025-08-07 20:03:59,641 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 20:03:59,641 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始改进版批量爬取任务
2025-08-07 20:03:59,641 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输入目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/xlsx_files
2025-08-07 20:03:59,641 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输出目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成
2025-08-07 20:03:59,642 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 20:03:59,642 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到 2 个待处理文件
2025-08-07 20:03:59,642 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理文件 1/2: XH-175327274434416.xlsx
2025-08-07 20:03:59,642 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理文件: XH-175327274434416.xlsx
2025-08-07 20:03:59,642 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:03:59,888 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从临时文件加载数据: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:03:59,888 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从上次中断的地方继续: 第 441 行
2025-08-07 20:03:59,889 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 441: https://mp.weixin.qq.com/s/_3M7lXN2X0nD7dg7uXU9Rw
2025-08-07 20:03:59,889 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s/_3M7lXN2X0nD7dg7uXU9Rw
2025-08-07 20:04:10,459 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 数据获取过程中出现异常: 'NoSuchDriverException' object is not callable
2025-08-07 20:04:10,459 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: ❌ URL 441 处理失败
2025-08-07 20:04:10,564 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:04:10,685 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 441: 标题长度 3, 内容长度 3
2025-08-07 20:04:10,686 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 7.4 秒...
2025-08-07 20:04:18,071 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 442: https://mp.weixin.qq.com/s/IIT0eO0vx6BQBVyMkKJXpA
2025-08-07 20:04:18,072 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s/IIT0eO0vx6BQBVyMkKJXpA
2025-08-07 20:04:23,161 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 数据获取过程中出现异常: 'NoSuchDriverException' object is not callable
2025-08-07 20:04:23,161 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: ❌ URL 442 处理失败
2025-08-07 20:04:23,264 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:04:23,383 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 442: 标题长度 3, 内容长度 3
2025-08-07 20:04:23,383 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 6.4 秒...
2025-08-07 20:04:29,783 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 443: https://mp.weixin.qq.com/s/YEkUrJHyMTrJ93MK_vhLdg
2025-08-07 20:04:29,783 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s/YEkUrJHyMTrJ93MK_vhLdg
2025-08-07 20:04:29,784 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败: Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-08-07 20:04:29,784 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败
2025-08-07 20:04:29,784 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: ❌ URL 443 处理失败
2025-08-07 20:04:29,884 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:04:30,013 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 443: 标题长度 3, 内容长度 3
2025-08-07 20:04:30,013 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 5.2 秒...
2025-08-07 20:05:41,645 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 20:05:41,645 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始改进版批量爬取任务
2025-08-07 20:05:41,645 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输入目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/xlsx_files
2025-08-07 20:05:41,645 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输出目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成
2025-08-07 20:05:41,645 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 20:05:41,645 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到 2 个待处理文件
2025-08-07 20:05:41,645 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理文件 1/2: XH-175327274434416.xlsx
2025-08-07 20:05:41,645 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理文件: XH-175327274434416.xlsx
2025-08-07 20:05:41,645 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:05:41,741 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从临时文件加载数据: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:05:41,741 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从上次中断的地方继续: 第 444 行
2025-08-07 20:05:41,741 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 444: https://mp.weixin.qq.com/s/Wx7oVIyjOgb9Gp_wf4zgRQ
2025-08-07 20:05:41,741 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s/Wx7oVIyjOgb9Gp_wf4zgRQ
2025-08-07 20:05:41,742 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败: Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-08-07 20:05:41,742 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败
2025-08-07 20:05:41,742 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: ❌ URL 444 处理失败
2025-08-07 20:05:41,791 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:05:41,828 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 444: 标题长度 3, 内容长度 3
2025-08-07 20:05:41,828 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 5.1 秒...
2025-08-07 20:05:46,917 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 445: https://mp.weixin.qq.com/s/C0I1vKyqUyUB75MTtGcTFQ
2025-08-07 20:05:46,920 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s/C0I1vKyqUyUB75MTtGcTFQ
2025-08-07 20:05:46,921 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败: Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-08-07 20:05:46,921 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:54] - ERROR: 浏览器初始化失败
2025-08-07 20:05:46,921 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:72] - WARNING: ❌ URL 445 处理失败
2025-08-07 20:05:46,977 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:05:47,014 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 445: 标题长度 3, 内容长度 3
2025-08-07 20:05:47,015 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 5.8 秒...
2025-08-07 20:08:30,775 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 20:08:30,775 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始改进版批量爬取任务
2025-08-07 20:08:30,775 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输入目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/xlsx_files
2025-08-07 20:08:30,775 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输出目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成
2025-08-07 20:08:30,775 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 20:08:30,775 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到 2 个待处理文件
2025-08-07 20:08:30,775 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理文件 1/2: XH-175327274434416.xlsx
2025-08-07 20:08:30,776 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理文件: XH-175327274434416.xlsx
2025-08-07 20:08:30,776 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:08:30,881 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从临时文件加载数据: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:08:30,881 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从上次中断的地方继续: 第 446 行
2025-08-07 20:08:30,882 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 446: http://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247492913&idx=1&sn=c2435e4c7ff76979cbf55f68076bcad3&scene=0
2025-08-07 20:08:30,882 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: http://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247492913&idx=1&sn=c2435e4c7ff76979cbf55f68076bcad3&scene=0
2025-08-07 20:08:36,404 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用Chrome浏览器（本地ChromeDriver）
2025-08-07 20:08:40,946 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 1 次检测页面类型
2025-08-07 20:08:40,956 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247492913&idx=1&sn=c2435e4c7ff76979cbf55f68076bcad3&scene=0&poc_token=HESXlGijNRxXtcIHYNS6OAwiwgnifX50-KxRBmkU
2025-08-07 20:08:40,959 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 账号已迁移
2025-08-07 20:08:40,974 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: weui-msg元素存在但不包含验证文本，可能是文章页面
2025-08-07 20:08:40,984 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面元素
2025-08-07 20:08:40,987 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移标题: 该公众号已迁移
2025-08-07 20:08:40,991 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493852&idx=1&sn=a22753758634cc5d683a25a4c8d6e978&source=41#wechat_redirect
2025-08-07 20:08:40,991 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面，处理迁移...
2025-08-07 20:08:40,992 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理迁移页面...
2025-08-07 20:08:40,997 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493852&idx=1&sn=a22753758634cc5d683a25a4c8d6e978&source=41#wechat_redirect
2025-08-07 20:08:41,844 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 点击迁移链接成功
2025-08-07 20:08:46,265 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理完成
2025-08-07 20:08:46,266 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理成功，继续检测...
2025-08-07 20:08:48,271 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 2 次检测页面类型
2025-08-07 20:08:48,370 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493852&idx=1&sn=a22753758634cc5d683a25a4c8d6e978&source=41&poc_token=HEmXlGijA7P_zwtOipBYqKmfBl_HRQGYBdf8SFGN
2025-08-07 20:08:48,373 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 存储芯片，周而复始 | 权益趣点
2025-08-07 20:08:48,390 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到验证页面，标识: verify
2025-08-07 20:08:48,398 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: weui-msg元素存在但不包含验证文本，可能是文章页面
2025-08-07 20:08:48,405 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到迁移页面元素: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//div[@class="weui-msg__opr-area"]/p/a[1]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x00000001010ad660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x00000001010a55d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x0000000100bf60fc cxxbridge1$string$len + 90068
3   chromedriver                        0x0000000100c3d4a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x0000000100c7e874 cxxbridge1$string$len + 649036
5   chromedriver                        0x0000000100c31790 cxxbridge1$string$len + 333416
6   chromedriver                        0x0000000101071440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x00000001010746a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x0000000101052328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x0000000101074f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x0000000101043414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x0000000101095090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x000000010109521c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x00000001010a5214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-07 20:08:48,516 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到文章内容元素
2025-08-07 20:08:48,528 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文章内容长度: 2704
2025-08-07 20:08:48,528 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到文章页面，立即提取数据...
2025-08-07 20:08:48,528 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始提取文章数据...
2025-08-07 20:08:48,538 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从meta标签提取到标题: 存储芯片，周而复始 | 权益趣点...
2025-08-07 20:08:48,567 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 提取到内容: 2704 字符
2025-08-07 20:08:48,569 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据提取完成
2025-08-07 20:08:48,569 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据提取成功
2025-08-07 20:08:48,672 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 提取到数据 - 标题长度: 16, 内容长度: 2667
2025-08-07 20:08:48,781 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据写入验证成功 (第1次验证) - 标题: 16, 内容: 2667
2025-08-07 20:08:48,781 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ✅ URL 446 处理成功
2025-08-07 20:08:48,844 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:08:48,883 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 446: 标题长度 16, 内容长度 2667
2025-08-07 20:08:48,884 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 3.5 秒...
2025-08-07 20:08:52,380 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 447: https://mp.weixin.qq.com/s/VTtzWjQ5vFa4Pzy0SjBiAA
2025-08-07 20:08:52,380 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s/VTtzWjQ5vFa4Pzy0SjBiAA
2025-08-07 20:08:52,999 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用Chrome浏览器（本地ChromeDriver）
2025-08-07 20:08:56,564 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 1 次检测页面类型
2025-08-07 20:08:56,596 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s/VTtzWjQ5vFa4Pzy0SjBiAA
2025-08-07 20:08:56,601 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 账号已迁移
2025-08-07 20:08:56,622 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: weui-msg元素存在但不包含验证文本，可能是文章页面
2025-08-07 20:08:56,633 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面元素
2025-08-07 20:08:56,637 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移标题: 该公众号已迁移
2025-08-07 20:08:56,640 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493729&idx=1&sn=d609a09f52c03c850d614f0fe243ca10&source=41#wechat_redirect
2025-08-07 20:08:56,640 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面，处理迁移...
2025-08-07 20:08:56,640 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理迁移页面...
2025-08-07 20:08:56,646 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493729&idx=1&sn=d609a09f52c03c850d614f0fe243ca10&source=41#wechat_redirect
2025-08-07 20:08:57,081 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 点击迁移链接成功
2025-08-07 20:09:00,364 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理完成
2025-08-07 20:09:00,364 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理成功，继续检测...
2025-08-07 20:09:02,374 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 2 次检测页面类型
2025-08-07 20:09:02,385 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/mp/wappoc_appmsgcaptcha?poc_token=HFiXlGijaZlUiRSh60Trxuq7LbWoU4vFtFN4J0ht&target_url=https%3A%2F%2Fmp.weixin.qq.com%2Fs%3F__biz%3DMzkzNTkwMTcyNg%3D%3D%26mid%3D2247493729%26idx%3D1%26sn%3Dd609a09f52c03c850d614f0fe243ca10%26source%3D41#wechat_redirect
2025-08-07 20:09:02,388 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 微信公众平台
2025-08-07 20:09:02,388 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到验证页面URL标识
2025-08-07 20:09:02,388 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到验证页面，处理验证...
2025-08-07 20:09:02,389 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理验证页面...
2025-08-07 20:09:06,069 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第1次验证尝试...
2025-08-07 20:09:06,088 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到js_content元素: Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="js_content"]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x0000000102991660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x00000001029895d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x00000001024da0fc cxxbridge1$string$len + 90068
3   chromedriver                        0x00000001025214a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x0000000102562874 cxxbridge1$string$len + 649036
5   chromedriver                        0x0000000102515790 cxxbridge1$string$len + 333416
6   chromedriver                        0x0000000102955440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x00000001029586a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x0000000102936328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x0000000102958f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x0000000102927414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x0000000102979090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x000000010297921c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x0000000102989214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-07 20:09:06,097 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到文章标题元素: Message: no such element: Unable to locate element: {"method":"css selector","selector":"meta[property="og:title"]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x0000000102991660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x00000001029895d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x00000001024da0fc cxxbridge1$string$len + 90068
3   chromedriver                        0x00000001025214a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x0000000102562874 cxxbridge1$string$len + 649036
5   chromedriver                        0x0000000102515790 cxxbridge1$string$len + 333416
6   chromedriver                        0x0000000102955440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x00000001029586a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x0000000102936328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x0000000102958f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x0000000102927414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x0000000102979090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x000000010297921c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x0000000102989214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-07 20:09:06,132 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到验证按钮: 去验证
2025-08-07 20:09:07,444 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: JavaScript点击验证按钮成功
2025-08-07 20:09:07,444 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待验证完成...
2025-08-07 20:09:09,448 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: === 验证状态检查 (第1次检查，已等待2秒) ===
2025-08-07 20:09:09,896 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493729&idx=1&sn=d609a09f52c03c850d614f0fe243ca10&source=41&poc_token=HFiXlGijaZlUiRSh60Trxuq7LbWoU4vFtFN4J0ht
2025-08-07 20:09:09,898 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 微信公众平台
2025-08-07 20:09:09,908 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到文章内容元素
2025-08-07 20:09:09,912 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文章内容长度: 29
2025-08-07 20:09:09,912 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ✅ 验证完成，已跳转到文章页面 (等待了2秒)
2025-08-07 20:09:09,912 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 验证处理成功，继续检测...
2025-08-07 20:09:11,918 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 3 次检测页面类型
2025-08-07 20:09:12,023 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493729&idx=1&sn=d609a09f52c03c850d614f0fe243ca10&source=41&poc_token=HFiXlGijaZlUiRSh60Trxuq7LbWoU4vFtFN4J0ht
2025-08-07 20:09:12,025 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 小信敲黑板 | 给现金增利“划重点”（一）——现金增利三大特点
2025-08-07 20:09:12,042 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到验证页面，标识: verify
2025-08-07 20:09:12,049 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: weui-msg元素存在但不包含验证文本，可能是文章页面
2025-08-07 20:09:12,056 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到迁移页面元素: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//div[@class="weui-msg__opr-area"]/p/a[1]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x0000000102991660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x00000001029895d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x00000001024da0fc cxxbridge1$string$len + 90068
3   chromedriver                        0x00000001025214a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x0000000102562874 cxxbridge1$string$len + 649036
5   chromedriver                        0x0000000102515790 cxxbridge1$string$len + 333416
6   chromedriver                        0x0000000102955440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x00000001029586a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x0000000102936328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x0000000102958f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x0000000102927414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x0000000102979090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x000000010297921c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x0000000102989214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-07 20:09:12,149 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到文章内容元素
2025-08-07 20:09:12,154 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文章内容长度: 29
2025-08-07 20:09:12,154 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到文章页面，立即提取数据...
2025-08-07 20:09:12,154 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始提取文章数据...
2025-08-07 20:09:12,166 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从meta标签提取到标题: 小信敲黑板 |  给现金增利“划重点”（一）——现金增利三大特点...
2025-08-07 20:09:12,187 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 提取到内容: 27 字符
2025-08-07 20:09:12,187 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据提取完成
2025-08-07 20:09:12,187 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据提取成功
2025-08-07 20:09:12,277 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 提取到数据 - 标题长度: 27, 内容长度: 23
2025-08-07 20:09:12,380 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据写入验证成功 (第1次验证) - 标题: 27, 内容: 23
2025-08-07 20:09:12,380 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ✅ URL 447 处理成功
2025-08-07 20:09:12,418 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:09:12,454 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 447: 标题长度 27, 内容长度 23
2025-08-07 20:09:12,454 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 2.2 秒...
2025-08-07 20:09:14,682 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 448: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247493167&idx=1&sn=5edd8c3a4eb7ca085b718857f089dd5b&scene=0
2025-08-07 20:09:14,683 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247493167&idx=1&sn=5edd8c3a4eb7ca085b718857f089dd5b&scene=0
2025-08-07 20:09:15,252 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用Chrome浏览器（本地ChromeDriver）
2025-08-07 20:09:18,816 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 1 次检测页面类型
2025-08-07 20:09:18,832 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/mp/wappoc_appmsgcaptcha?poc_token=HG2XlGijroh9NyA4rT30PrWZwbq_S4QQykWu20C_&target_url=https%3A%2F%2Fmp.weixin.qq.com%2Fs%3F__biz%3DMzg5NDY4ODMzMw%3D%3D%26mid%3D2247493167%26idx%3D1%26sn%3D5edd8c3a4eb7ca085b718857f089dd5b%26scene%3D0
2025-08-07 20:09:18,835 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 微信公众平台
2025-08-07 20:09:18,835 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到验证页面URL标识
2025-08-07 20:09:18,835 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到验证页面，处理验证...
2025-08-07 20:09:18,836 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理验证页面...
2025-08-07 20:09:21,345 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第1次验证尝试...
2025-08-07 20:09:21,351 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到js_content元素: Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="js_content"]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x000000010077d660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x00000001007755d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x00000001002c60fc cxxbridge1$string$len + 90068
3   chromedriver                        0x000000010030d4a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x000000010034e874 cxxbridge1$string$len + 649036
5   chromedriver                        0x0000000100301790 cxxbridge1$string$len + 333416
6   chromedriver                        0x0000000100741440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x00000001007446a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x0000000100722328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x0000000100744f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x0000000100713414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x0000000100765090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x000000010076521c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x0000000100775214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-07 20:09:21,358 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到文章标题元素: Message: no such element: Unable to locate element: {"method":"css selector","selector":"meta[property="og:title"]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x000000010077d660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x00000001007755d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x00000001002c60fc cxxbridge1$string$len + 90068
3   chromedriver                        0x000000010030d4a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x000000010034e874 cxxbridge1$string$len + 649036
5   chromedriver                        0x0000000100301790 cxxbridge1$string$len + 333416
6   chromedriver                        0x0000000100741440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x00000001007446a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x0000000100722328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x0000000100744f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x0000000100713414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x0000000100765090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x000000010076521c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x0000000100775214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-07 20:09:21,388 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到验证按钮: 去验证
2025-08-07 20:09:22,903 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: JavaScript点击验证按钮成功
2025-08-07 20:09:22,903 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待验证完成...
2025-08-07 20:09:24,906 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: === 验证状态检查 (第1次检查，已等待2秒) ===
2025-08-07 20:09:24,915 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247493167&idx=1&sn=5edd8c3a4eb7ca085b718857f089dd5b&scene=0&poc_token=HG2XlGijroh9NyA4rT30PrWZwbq_S4QQykWu20C_
2025-08-07 20:09:24,918 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 账号已迁移
2025-08-07 20:09:24,925 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到js_content元素: Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="js_content"]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x000000010077d660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x00000001007755d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x00000001002c60fc cxxbridge1$string$len + 90068
3   chromedriver                        0x000000010030d4a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x000000010034e874 cxxbridge1$string$len + 649036
5   chromedriver                        0x0000000100301790 cxxbridge1$string$len + 333416
6   chromedriver                        0x0000000100741440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x00000001007446a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x0000000100722328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x0000000100744f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x0000000100713414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x0000000100765090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x000000010076521c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x0000000100775214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-07 20:09:24,931 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到文章标题元素: Message: no such element: Unable to locate element: {"method":"css selector","selector":"meta[property="og:title"]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x000000010077d660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x00000001007755d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x00000001002c60fc cxxbridge1$string$len + 90068
3   chromedriver                        0x000000010030d4a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x000000010034e874 cxxbridge1$string$len + 649036
5   chromedriver                        0x0000000100301790 cxxbridge1$string$len + 333416
6   chromedriver                        0x0000000100741440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x00000001007446a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x0000000100722328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x0000000100744f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x0000000100713414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x0000000100765090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x000000010076521c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x0000000100775214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-07 20:09:24,934 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到文章URL特征
2025-08-07 20:09:24,934 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ✅ 验证完成，已跳转到文章页面 (等待了2秒)
2025-08-07 20:09:24,934 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 验证处理成功，继续检测...
2025-08-07 20:09:26,937 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 2 次检测页面类型
2025-08-07 20:09:26,954 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247493167&idx=1&sn=5edd8c3a4eb7ca085b718857f089dd5b&scene=0&poc_token=HG2XlGijroh9NyA4rT30PrWZwbq_S4QQykWu20C_
2025-08-07 20:09:26,958 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 账号已迁移
2025-08-07 20:09:26,972 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: weui-msg元素存在但不包含验证文本，可能是文章页面
2025-08-07 20:09:26,985 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面元素
2025-08-07 20:09:26,989 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移标题: 该公众号已迁移
2025-08-07 20:09:26,993 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493855&idx=1&sn=d3341360694db1c90c4b980948c08fea&source=41#wechat_redirect
2025-08-07 20:09:26,993 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面，处理迁移...
2025-08-07 20:09:26,993 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理迁移页面...
2025-08-07 20:09:26,998 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493855&idx=1&sn=d3341360694db1c90c4b980948c08fea&source=41#wechat_redirect
2025-08-07 20:09:27,445 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 点击迁移链接成功
2025-08-07 20:09:30,647 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理完成
2025-08-07 20:09:30,647 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理成功，继续检测...
2025-08-07 20:11:13,378 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 20:11:13,379 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始改进版批量爬取任务
2025-08-07 20:11:13,379 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输入目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/xlsx_files
2025-08-07 20:11:13,379 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 输出目录: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成
2025-08-07 20:11:13,379 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ==================================================
2025-08-07 20:11:13,379 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到 2 个待处理文件
2025-08-07 20:11:13,379 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理文件 1/2: XH-175327274434416.xlsx
2025-08-07 20:11:13,379 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理文件: XH-175327274434416.xlsx
2025-08-07 20:11:13,379 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:11:13,481 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从临时文件加载数据: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:11:13,481 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从上次中断的地方继续: 第 448 行
2025-08-07 20:11:13,481 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 448: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247493167&idx=1&sn=5edd8c3a4eb7ca085b718857f089dd5b&scene=0
2025-08-07 20:11:13,481 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247493167&idx=1&sn=5edd8c3a4eb7ca085b718857f089dd5b&scene=0
2025-08-07 20:11:15,046 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用Chrome浏览器（本地ChromeDriver）
2025-08-07 20:11:18,613 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 1 次检测页面类型
2025-08-07 20:11:18,627 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247493167&idx=1&sn=5edd8c3a4eb7ca085b718857f089dd5b&scene=0&poc_token=HOOXlGijHXH3hSCs0JUdY9J5nXGIkxOKG8OWRu5P
2025-08-07 20:11:18,631 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 账号已迁移
2025-08-07 20:11:18,647 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: weui-msg元素存在但不包含验证文本，可能是文章页面
2025-08-07 20:11:18,657 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面元素
2025-08-07 20:11:18,660 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移标题: 该公众号已迁移
2025-08-07 20:11:18,665 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493855&idx=1&sn=d3341360694db1c90c4b980948c08fea&source=41#wechat_redirect
2025-08-07 20:11:18,665 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面，处理迁移...
2025-08-07 20:11:18,665 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理迁移页面...
2025-08-07 20:11:18,671 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493855&idx=1&sn=d3341360694db1c90c4b980948c08fea&source=41#wechat_redirect
2025-08-07 20:11:18,931 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 点击迁移链接成功
2025-08-07 20:11:23,059 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理完成
2025-08-07 20:11:23,060 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理成功，继续检测...
2025-08-07 20:11:25,065 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 2 次检测页面类型
2025-08-07 20:11:25,241 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493855&idx=1&sn=d3341360694db1c90c4b980948c08fea&source=41&poc_token=HOaXlGij0nFZCElYgb5MkLfm3Fb_7iuTCSlfkOyH
2025-08-07 20:11:25,253 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 宏观视角 | 2024年Q1：延续与前瞻
2025-08-07 20:11:25,302 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到验证页面，标识: verify
2025-08-07 20:11:25,318 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: weui-msg元素存在但不包含验证文本，可能是文章页面
2025-08-07 20:11:25,332 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到迁移页面元素: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//div[@class="weui-msg__opr-area"]/p/a[1]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x0000000104cd9660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x0000000104cd15d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x00000001048220fc cxxbridge1$string$len + 90068
3   chromedriver                        0x00000001048694a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x00000001048aa874 cxxbridge1$string$len + 649036
5   chromedriver                        0x000000010485d790 cxxbridge1$string$len + 333416
6   chromedriver                        0x0000000104c9d440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x0000000104ca06a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x0000000104c7e328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x0000000104ca0f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x0000000104c6f414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x0000000104cc1090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x0000000104cc121c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x0000000104cd1214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-07 20:11:25,457 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到文章内容元素
2025-08-07 20:11:25,467 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文章内容长度: 1472
2025-08-07 20:11:25,467 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到文章页面，立即提取数据...
2025-08-07 20:11:25,467 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始提取文章数据...
2025-08-07 20:11:25,478 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从meta标签提取到标题: 宏观视角 | 2024年Q1：延续与前瞻...
2025-08-07 20:11:25,517 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 提取到内容: 1472 字符
2025-08-07 20:11:25,519 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据提取完成
2025-08-07 20:11:25,519 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据提取成功
2025-08-07 20:11:25,609 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 提取到数据 - 标题长度: 20, 内容长度: 1463
2025-08-07 20:11:25,717 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据写入验证成功 (第1次验证) - 标题: 20, 内容: 1463
2025-08-07 20:11:25,717 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ✅ URL 448 处理成功
2025-08-07 20:11:25,785 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:11:25,822 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 448: 标题长度 20, 内容长度 1463
2025-08-07 20:11:25,823 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 4.0 秒...
2025-08-07 20:11:29,824 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 449: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247490592&idx=2&sn=7c519642e5fa5c5b834ab0c71a7bdd60&scene=0
2025-08-07 20:11:29,824 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247490592&idx=2&sn=7c519642e5fa5c5b834ab0c71a7bdd60&scene=0
2025-08-07 20:11:30,466 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用Chrome浏览器（本地ChromeDriver）
2025-08-07 20:11:34,059 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 1 次检测页面类型
2025-08-07 20:11:34,070 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247490592&idx=2&sn=7c519642e5fa5c5b834ab0c71a7bdd60&scene=0&poc_token=HPKXlGijfU7ZPY7CbQQc-m4CBYI3sDp3TaFb-yMX
2025-08-07 20:11:34,072 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 账号已迁移
2025-08-07 20:11:34,092 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: weui-msg元素存在但不包含验证文本，可能是文章页面
2025-08-07 20:11:34,105 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面元素
2025-08-07 20:11:34,109 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移标题: 该公众号已迁移
2025-08-07 20:11:34,113 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493805&idx=2&sn=2d13ed03e9fc99480087f7b9fbcda55e&source=41#wechat_redirect
2025-08-07 20:11:34,113 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面，处理迁移...
2025-08-07 20:11:34,113 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理迁移页面...
2025-08-07 20:11:34,119 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493805&idx=2&sn=2d13ed03e9fc99480087f7b9fbcda55e&source=41#wechat_redirect
2025-08-07 20:11:34,404 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 点击迁移链接成功
2025-08-07 20:11:38,633 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理完成
2025-08-07 20:11:38,633 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理成功，继续检测...
2025-08-07 20:11:40,640 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 2 次检测页面类型
2025-08-07 20:11:40,763 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493805&idx=2&sn=2d13ed03e9fc99480087f7b9fbcda55e&source=41&poc_token=HPaXlGij8STRe0EsiSDXKIYJ6KDkww6dTxmJAWNx
2025-08-07 20:11:40,765 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 国信证券资产管理总部诚聘英才，“职”等你来
2025-08-07 20:11:40,783 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到验证页面，标识: verify
2025-08-07 20:11:40,791 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: weui-msg元素存在但不包含验证文本，可能是文章页面
2025-08-07 20:11:40,797 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到迁移页面元素: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//div[@class="weui-msg__opr-area"]/p/a[1]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x000000010541d660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x00000001054155d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x0000000104f660fc cxxbridge1$string$len + 90068
3   chromedriver                        0x0000000104fad4a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x0000000104fee874 cxxbridge1$string$len + 649036
5   chromedriver                        0x0000000104fa1790 cxxbridge1$string$len + 333416
6   chromedriver                        0x00000001053e1440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x00000001053e46a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x00000001053c2328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x00000001053e4f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x00000001053b3414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x0000000105405090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x000000010540521c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x0000000105415214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-07 20:11:40,939 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到文章内容元素
2025-08-07 20:11:40,967 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文章内容长度: 1316
2025-08-07 20:11:40,967 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到文章页面，立即提取数据...
2025-08-07 20:11:40,967 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始提取文章数据...
2025-08-07 20:11:40,976 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从meta标签提取到标题: 国信证券资产管理总部诚聘英才，“职”等你来...
2025-08-07 20:11:41,007 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 提取到内容: 1316 字符
2025-08-07 20:11:41,008 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据提取完成
2025-08-07 20:11:41,008 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据提取成功
2025-08-07 20:11:41,092 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 提取到数据 - 标题长度: 19, 内容长度: 1294
2025-08-07 20:11:41,194 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据写入验证成功 (第1次验证) - 标题: 19, 内容: 1294
2025-08-07 20:11:41,194 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ✅ URL 449 处理成功
2025-08-07 20:11:41,238 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:11:41,274 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 449: 标题长度 19, 内容长度 1294
2025-08-07 20:11:41,275 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 3.8 秒...
2025-08-07 20:11:45,047 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 450: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247486359&idx=1&sn=d75d02f3fb4fb1848d1d0f90642a3818&scene=0
2025-08-07 20:11:45,049 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247486359&idx=1&sn=d75d02f3fb4fb1848d1d0f90642a3818&scene=0
2025-08-07 20:11:45,616 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用Chrome浏览器（本地ChromeDriver）
2025-08-07 20:11:49,077 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 1 次检测页面类型
2025-08-07 20:11:49,084 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/mp/wappoc_appmsgcaptcha?poc_token=HAOYlGijz25Mvs6Hndn1aI-4AiS5ckMGvJx064xE&target_url=https%3A%2F%2Fmp.weixin.qq.com%2Fs%3F__biz%3DMzg5NDY4ODMzMw%3D%3D%26mid%3D2247486359%26idx%3D1%26sn%3Dd75d02f3fb4fb1848d1d0f90642a3818%26scene%3D0
2025-08-07 20:11:49,086 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 微信公众平台
2025-08-07 20:11:49,086 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到验证页面URL标识
2025-08-07 20:11:49,086 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到验证页面，处理验证...
2025-08-07 20:11:49,086 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理验证页面...
2025-08-07 20:11:53,072 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第1次验证尝试...
2025-08-07 20:11:53,085 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到js_content元素: Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="js_content"]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x0000000104519660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x00000001045115d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x00000001040620fc cxxbridge1$string$len + 90068
3   chromedriver                        0x00000001040a94a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x00000001040ea874 cxxbridge1$string$len + 649036
5   chromedriver                        0x000000010409d790 cxxbridge1$string$len + 333416
6   chromedriver                        0x00000001044dd440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x00000001044e06a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x00000001044be328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x00000001044e0f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x00000001044af414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x0000000104501090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x000000010450121c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x0000000104511214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-07 20:11:53,094 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到文章标题元素: Message: no such element: Unable to locate element: {"method":"css selector","selector":"meta[property="og:title"]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x0000000104519660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x00000001045115d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x00000001040620fc cxxbridge1$string$len + 90068
3   chromedriver                        0x00000001040a94a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x00000001040ea874 cxxbridge1$string$len + 649036
5   chromedriver                        0x000000010409d790 cxxbridge1$string$len + 333416
6   chromedriver                        0x00000001044dd440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x00000001044e06a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x00000001044be328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x00000001044e0f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x00000001044af414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x0000000104501090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x000000010450121c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x0000000104511214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-07 20:11:53,132 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到验证按钮: 去验证
2025-08-07 20:11:54,598 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: JavaScript点击验证按钮成功
2025-08-07 20:11:54,598 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待验证完成...
2025-08-07 20:11:56,612 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: === 验证状态检查 (第1次检查，已等待2秒) ===
2025-08-07 20:11:56,619 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247486359&idx=1&sn=d75d02f3fb4fb1848d1d0f90642a3818&scene=0&poc_token=HAOYlGijz25Mvs6Hndn1aI-4AiS5ckMGvJx064xE
2025-08-07 20:11:56,622 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 账号已迁移
2025-08-07 20:11:56,628 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到js_content元素: Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="js_content"]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x0000000104519660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x00000001045115d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x00000001040620fc cxxbridge1$string$len + 90068
3   chromedriver                        0x00000001040a94a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x00000001040ea874 cxxbridge1$string$len + 649036
5   chromedriver                        0x000000010409d790 cxxbridge1$string$len + 333416
6   chromedriver                        0x00000001044dd440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x00000001044e06a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x00000001044be328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x00000001044e0f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x00000001044af414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x0000000104501090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x000000010450121c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x0000000104511214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-07 20:11:56,632 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到文章标题元素: Message: no such element: Unable to locate element: {"method":"css selector","selector":"meta[property="og:title"]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x0000000104519660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x00000001045115d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x00000001040620fc cxxbridge1$string$len + 90068
3   chromedriver                        0x00000001040a94a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x00000001040ea874 cxxbridge1$string$len + 649036
5   chromedriver                        0x000000010409d790 cxxbridge1$string$len + 333416
6   chromedriver                        0x00000001044dd440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x00000001044e06a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x00000001044be328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x00000001044e0f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x00000001044af414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x0000000104501090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x000000010450121c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x0000000104511214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-07 20:11:56,635 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到文章URL特征
2025-08-07 20:11:56,635 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ✅ 验证完成，已跳转到文章页面 (等待了2秒)
2025-08-07 20:11:56,635 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 验证处理成功，继续检测...
2025-08-07 20:11:58,640 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 2 次检测页面类型
2025-08-07 20:11:58,650 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247486359&idx=1&sn=d75d02f3fb4fb1848d1d0f90642a3818&scene=0&poc_token=HAOYlGijz25Mvs6Hndn1aI-4AiS5ckMGvJx064xE
2025-08-07 20:11:58,652 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 账号已迁移
2025-08-07 20:11:58,662 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: weui-msg元素存在但不包含验证文本，可能是文章页面
2025-08-07 20:11:58,674 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面元素
2025-08-07 20:11:58,679 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移标题: 该公众号已迁移
2025-08-07 20:11:58,683 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493725&idx=1&sn=740a7ea1be603028b59b0393fff5eb47&source=41#wechat_redirect
2025-08-07 20:11:58,683 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面，处理迁移...
2025-08-07 20:11:58,683 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理迁移页面...
2025-08-07 20:11:58,688 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493725&idx=1&sn=740a7ea1be603028b59b0393fff5eb47&source=41#wechat_redirect
2025-08-07 20:11:58,980 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 点击迁移链接成功
2025-08-07 20:12:04,348 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理完成
2025-08-07 20:12:04,348 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理成功，继续检测...
2025-08-07 20:12:06,351 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 3 次检测页面类型
2025-08-07 20:12:06,466 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493725&idx=1&sn=740a7ea1be603028b59b0393fff5eb47&source=41&poc_token=HA6YlGijG3qupxCCSoEUFe0Z7oLKItcmsCeMl8ia
2025-08-07 20:12:06,469 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 国信现金增利 | 你的专属“零钱包”
2025-08-07 20:12:06,486 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到验证页面，标识: verify
2025-08-07 20:12:06,495 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: weui-msg元素存在但不包含验证文本，可能是文章页面
2025-08-07 20:12:06,502 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 未找到迁移页面元素: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//div[@class="weui-msg__opr-area"]/p/a[1]"}
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
0   chromedriver                        0x0000000104519660 cxxbridge1$str$ptr + 2724392
1   chromedriver                        0x00000001045115d8 cxxbridge1$str$ptr + 2691488
2   chromedriver                        0x00000001040620fc cxxbridge1$string$len + 90068
3   chromedriver                        0x00000001040a94a0 cxxbridge1$string$len + 381816
4   chromedriver                        0x00000001040ea874 cxxbridge1$string$len + 649036
5   chromedriver                        0x000000010409d790 cxxbridge1$string$len + 333416
6   chromedriver                        0x00000001044dd440 cxxbridge1$str$ptr + 2478088
7   chromedriver                        0x00000001044e06a8 cxxbridge1$str$ptr + 2490992
8   chromedriver                        0x00000001044be328 cxxbridge1$str$ptr + 2350832
9   chromedriver                        0x00000001044e0f64 cxxbridge1$str$ptr + 2493228
10  chromedriver                        0x00000001044af414 cxxbridge1$str$ptr + 2289628
11  chromedriver                        0x0000000104501090 cxxbridge1$str$ptr + 2624600
12  chromedriver                        0x000000010450121c cxxbridge1$str$ptr + 2624996
13  chromedriver                        0x0000000104511214 cxxbridge1$str$ptr + 2690524
14  libsystem_pthread.dylib             0x00000001a235826c _pthread_start + 148
15  libsystem_pthread.dylib             0x00000001a235308c thread_start + 8

2025-08-07 20:12:06,597 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到文章内容元素
2025-08-07 20:12:06,602 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文章内容长度: 20
2025-08-07 20:12:06,602 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到文章页面，立即提取数据...
2025-08-07 20:12:06,602 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始提取文章数据...
2025-08-07 20:12:06,609 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 从meta标签提取到标题: 国信现金增利 | 你的专属“零钱包”...
2025-08-07 20:12:06,627 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 提取到内容: 18 字符
2025-08-07 20:12:06,628 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据提取完成
2025-08-07 20:12:06,628 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据提取成功
2025-08-07 20:12:06,715 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 提取到数据 - 标题长度: 16, 内容长度: 16
2025-08-07 20:12:06,818 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 数据写入验证成功 (第1次验证) - 标题: 16, 内容: 16
2025-08-07 20:12:06,818 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: ✅ URL 450 处理成功
2025-08-07 20:12:06,862 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 已保存临时文件: /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat-1/回查完成/temp_improved_XH-175327274434416.xlsx
2025-08-07 20:12:06,899 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 文件保存验证成功 - 行 450: 标题长度 16, 内容长度 16
2025-08-07 20:12:06,900 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 等待 2.2 秒...
2025-08-07 20:12:09,144 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 处理URL 451: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247489365&idx=1&sn=f97f0d7d9cf33796c2ca13201554f55d&scene=0
2025-08-07 20:12:09,145 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247489365&idx=1&sn=f97f0d7d9cf33796c2ca13201554f55d&scene=0
2025-08-07 20:12:09,838 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 使用Chrome浏览器（本地ChromeDriver）
2025-08-07 20:12:13,460 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 第 1 次检测页面类型
2025-08-07 20:12:13,472 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 当前URL: https://mp.weixin.qq.com/s?__biz=Mzg5NDY4ODMzMw==&mid=2247489365&idx=1&sn=f97f0d7d9cf33796c2ca13201554f55d&scene=0&poc_token=HBqYlGijpquZa3Bng62edwzWdqnxiIaIOtoAVuiG
2025-08-07 20:12:13,475 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 页面标题: 账号已迁移
2025-08-07 20:12:13,495 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: weui-msg元素存在但不包含验证文本，可能是文章页面
2025-08-07 20:12:13,508 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面元素
2025-08-07 20:12:13,512 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移标题: 该公众号已迁移
2025-08-07 20:12:13,516 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493762&idx=1&sn=9bcc1ee902ad80bc6b2be1acff7cdceb&source=41#wechat_redirect
2025-08-07 20:12:13,516 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 检测到迁移页面，处理迁移...
2025-08-07 20:12:13,516 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 开始处理迁移页面...
2025-08-07 20:12:13,521 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 找到迁移链接: http://mp.weixin.qq.com/s?__biz=MzkzNTkwMTcyNg==&mid=2247493762&idx=1&sn=9bcc1ee902ad80bc6b2be1acff7cdceb&source=41#wechat_redirect
2025-08-07 20:12:13,816 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 点击迁移链接成功
2025-08-07 20:12:17,665 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理完成
2025-08-07 20:12:17,666 - /Users/<USER>/Downloads/botSmart-日常业务/回查任务/lookback-webo-and-wechat/src/log/Logger.py[line:63] - INFO: 迁移处理成功，继续检测...
