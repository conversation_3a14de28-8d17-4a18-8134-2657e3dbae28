# -*- coding: utf-8 -*-
import threading

from flask import Flask
from flask_cors import CORS

from src.entity import Agent
from src.log import Logger
from src.utils import RabbitMQUtils

app = Flask(__name__)
CORS(app, supports_credentials=True)

try:
    Logger.Log.init(printflag=True)
    webo_task_reciver = threading.Thread(target=RabbitMQUtils.Helper.webo_task_reciver)
    webo_task_reciver.start()
    wechat_task_reciver = threading.Thread(target=RabbitMQUtils.Helper.wechat_task_reciver)
    wechat_task_reciver.start()
    app.run(host='127.0.0.1', port=Agent.Param.port)
except Exception as e:
    Logger.Log.error(e.__str__())
